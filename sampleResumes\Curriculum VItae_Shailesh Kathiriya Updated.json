{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Gender": "Male", "BirthDate": "29thMarch1998", "Email": "ka<PERSON><PERSON><PERSON><PERSON><EMAIL>", "Address": "ActualFullAddressInString", "ContactNumber": "7202909521", "LinkedInProfile": ""}, "Objective": "To be a part of an organization where I can apply and enhance my technical skills and\n  knowledge. I would like to do work which can provide me an insight into new aspects so \nthat it would be helpful for professional and personal growth. ", "Education": [{"Degree": "HSC", "Institution": "Akshay High School", "GraduationYear": "2015", "GPA/Marks/%": "70%"}, {"Degree": "(<PERSON><PERSON>)", "Institution": "GLS University", "GraduationYear": "2019", "GPA/Marks/%": ""}, {"Degree": " (<PERSON><PERSON>) ", "Institution": "Gujarat University. ", "GraduationYear": "2021", "GPA/Marks/%": ""}], "WorkExperience": [{"CompanyName": "Neuberg Diagnostics Private Limited. ", "Role": "Senior Account Executive", "StartYear": "Sep-2019", "EndYear": "'Present'", "Description/Responsibility": "Preparing MIS reports of Sister-concerns.\n Procurement Booking, Payment Control and Coordination with Vendors \n for ledger reconciliations.\n   Preparation of Bank Reconciliation, TDS Reconciliation.\n Preparations of TDS/TCS working and Payment there of. \n  Controlling the day-to-day cash collections and co-ordination thereof at various\n  locations.\n   Bookkeeping of Sister-concerns in tally up to finalization of accounts and audit.\n  Preparations of Monthly and Weekly reports from books of accounts as required \n by management .\n  Comprehensive knowledge of procedures related to import payments.\n   Work in SAP (S4 Hana) and Tally Prime.\n  "}, {"CompanyName": "CSR Digital & Smart\n Education Centre", "Role": "professor", "StartYear": "June-2017", "EndYear": "'June-2018'", "Description/Responsibility": "Teaching Tally ERP.9 with GST. \n Teaching CCC course.  "}, {"CompanyName": "Bhadreswar Traders &\nChellenj Marketing.  ", "Role": "accountant", "StartYear": "July-2018", "EndYear": "'Sep-2019'", "Description/Responsibility": "Preparing a invoice of Sales & Purchases.\n Preparing of Bank Reconciliation. \nControlling day-to-day cash collections."}], "Skills": ["Microsoft Advance Excel Functions (365)\n Microsoft Power BI (Business Intelligence) \nMicrosoft Power Query \nAutomation - Experience with automating repetitive tasks and processes using\n AI tools and software, such as robotic process automation (RPA). "], "Certifications": [{"CertificationName": " Computer Concept (CCC)", "IssuingOrganization": "y HI-TECH Education Class", "IssueDate": "", "ExpiryDate": "'None'"}, {"CertificationName": "Tally ERP.9 ", "IssuingOrganization": "HI-TECH Education Class", "IssueDate": "", "ExpiryDate": "'None'"}, {"CertificationName": " Advance Excel Functions", "IssuingOrganization": "Microsoft", "IssueDate": "", "ExpiryDate": "'None'"}, {"CertificationName": " Power BI", "IssuingOrganization": "Microsoft", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [" English, Gujarati & Hindi. "], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}