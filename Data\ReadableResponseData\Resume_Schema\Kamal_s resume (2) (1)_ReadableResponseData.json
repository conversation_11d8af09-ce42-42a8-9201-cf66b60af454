{"Resume": {"PersonalInformation": {"FullName": "<PERSON>", "Gender": "Male", "BirthDate": 19900101, "Email": "<EMAIL>", "Address": "24 Manushri Nagar, Indore", "ContactNumber": "7974346268", "LinkedInProfile": "https://www.linkedin.com/in/ka<PERSON><PERSON>"}, "Objective": "Dedicated professional with a background in teaching and IT administration seeking a challenging role where I can utilize my skills to contribute to the success of an organization. Committed to fostering a positive learning environment and effectively managing technology resources to streamline operations and enhance productivity.", "Education": [{"Degree": "M.B.A.", "Institution": "DAVV, Indore", "GraduationYear": "2023", "GPA/Marks/%": 0}, {"Degree": "B.E.", "Institution": "RGPV", "GraduationYear": "2020", "GPA/Marks/%": 0}, {"Degree": "Higher Secondary School", "Institution": "SKBVN", "GraduationYear": "2016", "GPA/Marks/%": 0}, {"Degree": "High School", "Institution": "SKBVN", "GraduationYear": "2014", "GPA/Marks/%": 0}], "WorkExperience": [{"CompanyName": "<PERSON> <PERSON>, Chhatribagh", "Role": "CS Faculty & IT Administrator", "StartYear": 201901, "EndYear": 202112, "Description/Responsibility": "Implemented technology-based teaching tools to enhance student learning experience and engagement in the classroom. Provided technical support and troubleshooting assistance to faculty and staff, resulting in increased efficiency and productivity. Managed and maintained educational software and hardware systems, ensuring optimal performance and functionality. Collaborated with IT department to develop and implement data management strategies for student assessments and academic records."}, {"CompanyName": "Techforce Global Pvt. Ltd. (Ahmedabad)", "Role": "Software Testing Engineer", "StartYear": 202201, "EndYear": 202312, "Description/Responsibility": "Developing comprehensive test plans and test cases to ensure thorough software validation. Executing manual and automated tests using Selenium with Java to identify defects and ensure optimal product quality. Participating in agile development processes to deliver high-quality software within project timelines. Leveraging Java's object-oriented capabilities to create robust and maintainable test scripts. Contributing to the enhancement of testing methodologies and tools, with a focus on Java-based automation for efficient testing."}, {"CompanyName": "Atomic Systems Infotech (Bangalore)", "Role": "Software Testing Engineer", "StartYear": 202001, "EndYear": 202112, "Description/Responsibility": "Conducted thorough testing of software applications to ensure functionality, usability, and compatibility across multiple platforms. Developed and executed test cases, test scripts, and test scenarios to validate software functionality and performance."}], "TotalWorkExperienceInYears": 5, "Skills": ["Python", "MySQL", "SQL", "Troubleshooting and Technical Support", "Active Directory", "Software Install/Uninstall", "Computer Networking", "Continuous Learning", "Adaptability", "Problem-Solving and Critical Thinking"], "Certifications": [], "Achievements": [], "Languages": ["English", "Hindi"], "Projects": []}}