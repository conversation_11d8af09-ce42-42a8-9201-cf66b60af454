{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON><PERSON>", "Gender": "", "BirthDate": "07/08/2023", "Email": "<EMAIL>", "Address": "Ahmedabad, India 382481", "ContactNumber": "+91 9099636655", "LinkedInProfile": ""}, "Objective": "", "Education": [{"Degree": "Master of Commerce", "Institution": "Ganpat University - Kherva", "GraduationYear": "2016", "GPA/Marks/%": "CGPA-6.50"}, {"Degree": "Bachelor of Commerce", "Institution": "N.S.V.K.M Science & Commerce Collage - Visnagar", "GraduationYear": "2014", "GPA/Marks/%": "CGPA-6.58"}, {"Degree": "Diploma - Electronics And Communication  ", "Institution": "San<PERSON><PERSON>d Patel College of Engineering - Visnagar", "GraduationYear": "2011", "GPA/Marks/%": "<PERSON><PERSON><PERSON>"}, {"Degree": "GSHSEB-12th", "Institution": "Sheth M R S High School - Unjha", "GraduationYear": "2010", "GPA/Marks/%": "59.57%"}, {"Degree": "GSHSEB–10th", "Institution": "GL Patel High School, - Unjha", "GraduationYear": "2008", "GPA/Marks/%": "67.38%"}], "WorkExperience": [{"CompanyName": "NSEG Private Limited – Ahmedabad", "Role": "Accountant", "StartYear": "01/2019", "EndYear": "'Current'", "Description/Responsibility": "Tracking all incoming and outgoing payments and reviewing accounts receivable and\npayable\nTo prepare monthly profit and loss and balance sheet reports and all other required financial\nstatements\nMaking day-to-day payments domestically and internationally in accordance with the\ndeadlines\nAccount reconciliation with the bank and reviewing expense data, net worth, and assets\nPerforming day-to-day financial transactions, verifying and recording accounts payable\ndata\nTaking follow-up for the payments of the all-overdue invoices\nManaging accounting tasks for General Ledger, including journal entries and month-end\ncloseouts\nMaking weekly cash flow reports and keeping expenditure and cash flow under control\nAssist with workers' compensation insurance working\nPreparation and timely reporting of monthly BAS, IAS, Payroll Tax, Superannuation and other\nstatutory requirements\nPreparation of payroll for staff (weekly and monthly pay runs)\nMaintaining and calculating employee leave entitlements\nPreparing monthly financial reports and all other management reports as required by\nfinancial controller and Directors\nAssist with payroll tax calculation\nLiaison with external accountants for payroll, super, payroll tax, accounts, etc\nTeam management and employee training as and when required"}, {"CompanyName": "P And N NSW Pty Ltd – Ahmedabad", "Role": "Accountant", "StartYear": "11/2015", "EndYear": "' 12/2018'", "Description/Responsibility": "Day-to-day accounting tasks to keep accounting very accurate\nManaging accounts payable and accounts receivable\nFollow-ups are made for all overdue invoices for payments\nHandle Company bookkeeping and payroll activities, including data input and payroll\nprocessing\nRecord all accounting transactions in software and prepare bank reconciliations\nMaking day-to-day payments domestically and internationally in accordance with the\ndeadlines\nAssist with workers' compensation insurance working\nManage general ledger accounting tasks, including journal entries and month-end closing\nactivities\nAssist with payroll tax calculation\nCreating weekly cash flow figures and keeping expenditure and cash flow under control\nLiaison with external accountants for payroll, super, payroll tax, accounts, etc\nReview and Prepare BAS and ensure compliance with the Australian Taxation Office (ATO)\nTeam management and employee training as and when required"}], "Skills": ["Intermediate MS Excel skills, including pivot\ntables, VLOOKUPs, formulas, and XLOOKUPs\nExperience with Xero/MYOB/QuickBooks\nMicrosoft Dynamics NAV\nAbility to manage multiple priorities and\nwork deadlines\nExcellent analytical and problem-solving\nskills.\nStrong written and verbal communication\nHandling payroll\nForecasting budgets\nEffective communication and teamwork\nskills"], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [""], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}