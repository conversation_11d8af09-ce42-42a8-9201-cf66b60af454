.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.sidebar.collapsed {
  width: 80px;
}

/* Header */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.sidebar.collapsed .sidebar-header {
  padding: 15px 8px;
  justify-content: space-between;
}

.logo-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.logo-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.logo-collapsed:hover {
  background-color: var(--bg-tertiary);
  transform: scale(1.05);
}

.logo-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  background: transparent;
  /* Remove any background color from the image */
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  /* Enhance contrast if needed */
  filter: contrast(1.1) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.2s ease;
}

.logo-icon.clickable {
  cursor: pointer;
}

.logo-icon.clickable:hover {
  transform: scale(1.1);
}

.sidebar.collapsed .logo-icon {
  width: 36px;
  height: 36px;
}

.logo-title {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hide logo title in collapsed state */
.sidebar.collapsed .logo-title {
  display: none;
}

/* Hide toggle button in collapsed state - logo click handles expansion */
.sidebar.collapsed .toggle-btn {
  display: none;
}

.toggle-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.toggle-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.sidebar.collapsed .toggle-btn {
  margin: 0 auto;
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  border-radius: 0;
  margin: 0 12px;
  border-radius: 8px;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  margin: 0 16px;
  padding: 12px;
}

.nav-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-link.active {
  background-color: var(--bg-tertiary);
  color: var(--accent-primary);
  font-weight: 500;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  transition: color 0.2s ease;
}

.nav-label {
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  border-radius: 2px;
  background-color: #3b82f6;
}

.sidebar.collapsed .active-indicator {
  display: none;
}

/* Footer */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-primary);
  background: var(--bg-tertiary);
}

.logout-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 0;
  background: none;
  border: none;
  color: var(--error);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--error);
}

.logout-btn .nav-label {
  color: inherit;
}

.sidebar-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-primary);
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.version-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.powered-by {
  font-size: 11px;
  color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 80px;
  }

  .sidebar .nav-link {
    justify-content: center;
    margin: 0 16px;
    padding: 12px;
  }

  .sidebar .nav-label {
    display: none;
  }

  .sidebar .active-indicator {
    display: none;
  }

  .sidebar .sidebar-info {
    display: none;
  }

  .sidebar .logout-btn {
    justify-content: center;
  }

  .sidebar .logout-btn .nav-label {
    display: none;
  }

  .sidebar .logo-title {
    display: none;
  }

  .sidebar .toggle-btn {
    display: none;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }
  
  .sidebar.collapsed {
    transform: translateX(0);
    width: 80px;
  }
}

/* Scrollbar for navigation */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation for nav items */
.nav-item {
  animation: slideInLeft 0.3s ease-out;
  animation-fill-mode: both;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.15s; }
.nav-item:nth-child(3) { animation-delay: 0.2s; }
.nav-item:nth-child(4) { animation-delay: 0.25s; }
.nav-item:nth-child(5) { animation-delay: 0.3s; }
.nav-item:nth-child(6) { animation-delay: 0.35s; }
.nav-item:nth-child(7) { animation-delay: 0.4s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
