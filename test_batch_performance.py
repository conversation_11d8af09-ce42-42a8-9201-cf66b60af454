"""
High-Performance Batch Processing Test Script

This script demonstrates the optimized batch processing capabilities
for both MongoDB and ChromaDB insertions.
"""

import asyncio
import os
import time
from mongoDBInsertion import process_documents_from_folder

async def test_batch_performance():
    """Test different batch processing configurations for performance comparison."""
    
    # Test folder path - adjust this to your actual test folder
    test_folder = r"\\************\user_data\PAVAN\Documents\resumes"
    
    if not os.path.exists(test_folder):
        print(f"Test folder not found: {test_folder}")
        print("Please update the test_folder path in this script.")
        return
    
    print("🚀 High-Performance Batch Processing Test")
    print("=" * 60)
    
    # Test configurations
    test_configs = [
        {
            "name": "Standard Configuration",
            "workers": 12,
            "batch_size": 50,
            "mongodb_batch_size": 100
        }
    ]
    
    results_summary = []
    
    for config in test_configs:
        print(f"\n🔧 Testing: {config['name']}")
        print(f"   Workers: {config['workers']}")
        print(f"   ChromaDB Batch Size: {config['batch_size']}")
        print(f"   MongoDB Batch Size: {config['mongodb_batch_size']}")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            results = await process_documents_from_folder(
                folder_path=test_folder,
                database_name="dbProductionV2",
                collection_name="collectionResumeV2",
                n_workers=config['workers'],
                enable_chromadb=True,
                batch_size=config['batch_size'],
                mongodb_batch_size=config['mongodb_batch_size']
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Calculate performance metrics
            total_files = results.get('total_files', 0)
            processed_files = results.get('processed_successfully', 0)
            failed_files = results.get('failed_files', 0)
            duplicate_files = results.get('duplicate_files', 0)
            
            throughput = processed_files / total_time if total_time > 0 else 0
            avg_time_per_file = total_time / processed_files if processed_files > 0 else 0
            
            config_result = {
                'config': config['name'],
                'total_time': total_time,
                'total_files': total_files,
                'processed_files': processed_files,
                'failed_files': failed_files,
                'duplicate_files': duplicate_files,
                'throughput': throughput,
                'avg_time_per_file': avg_time_per_file
            }
            
            results_summary.append(config_result)
            
            print(f"✅ Results for {config['name']}:")
            print(f"   Total Time: {total_time:.2f} seconds")
            print(f"   Total Files: {total_files}")
            print(f"   Processed: {processed_files}")
            print(f"   Failed: {failed_files}")
            print(f"   Duplicates: {duplicate_files}")
            print(f"   Throughput: {throughput:.2f} files/second")
            print(f"   Avg Time/File: {avg_time_per_file:.2f} seconds")
            
        except Exception as e:
            print(f"❌ Error testing {config['name']}: {e}")
            import traceback
            traceback.print_exc()
    
    # Print comparison summary
    print("\n" + "=" * 60)
    print("📊 PERFORMANCE COMPARISON SUMMARY")
    print("=" * 60)
    
    if results_summary:
        print(f"{'Configuration':<35} {'Time(s)':<10} {'Files/s':<10} {'Avg(s)':<10}")
        print("-" * 65)
        
        for result in results_summary:
            print(f"{result['config']:<35} {result['total_time']:<10.2f} {result['throughput']:<10.2f} {result['avg_time_per_file']:<10.2f}")
        
        # Find best performing configuration
        best_config = max(results_summary, key=lambda x: x['throughput'])
        print(f"\n🏆 Best Performance: {best_config['config']}")
        print(f"   Throughput: {best_config['throughput']:.2f} files/second")
        
        # Performance improvement calculation
        if len(results_summary) > 1:
            baseline = results_summary[0]
            best = best_config
            improvement = ((best['throughput'] - baseline['throughput']) / baseline['throughput']) * 100
            print(f"   Improvement: {improvement:.1f}% faster than baseline")

async def test_single_configuration():
    """Test a single optimized configuration."""
    
    test_folder = r"\\************\user_data\PAVAN\Desktop\Gemini\resumes"
    
    if not os.path.exists(test_folder):
        print(f"Test folder not found: {test_folder}")
        return
    
    print("🚀 Single Configuration Performance Test")
    print("=" * 50)
    print("Configuration: Optimized for Speed")
    print("Workers: 20")
    print("ChromaDB Batch Size: 100")
    print("MongoDB Batch Size: 200")
    print("-" * 50)
    
    results = await process_documents_from_folder(
        folder_path=test_folder,
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        n_workers=20,
        enable_chromadb=True,
        batch_size=100,
        mongodb_batch_size=200
    )
    
    print("\n📊 Results:")
    print(f"Total files: {results.get('total_files', 0)}")
    print(f"Successfully processed: {results.get('processed_successfully', 0)}")
    print(f"Failed: {results.get('failed_files', 0)}")
    print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
    print(f"Processing time: {results.get('processing_time', 'N/A')}")
    
    if results.get('failed_files_list'):
        print(f"\nFailed files: {', '.join(results['failed_files_list'][:5])}")
        if len(results['failed_files_list']) > 5:
            print(f"... and {len(results['failed_files_list']) - 5} more")

def print_usage_examples():
    """Print usage examples for the optimized batch processing."""
    
    print("🔧 OPTIMIZED BATCH PROCESSING USAGE EXAMPLES")
    print("=" * 60)
    print()
    print("Command Line Usage:")
    print()
    print("# Standard optimized processing")
    print("python mongoDBInsertion.py /path/to/documents --workers 20 --batch-size 100 --mongodb-batch-size 200")
    print()
    print("# Maximum performance (adjust based on your system)")
    print("python mongoDBInsertion.py /path/to/documents --workers 24 --batch-size 150 --mongodb-batch-size 300")
    print()
    print("# Conservative settings for smaller systems")
    print("python mongoDBInsertion.py /path/to/documents --workers 8 --batch-size 50 --mongodb-batch-size 100")
    print()
    print("Programmatic Usage:")
    print()
    print("```python")
    print("import asyncio")
    print("from mongoDBInsertion import process_documents_from_folder")
    print()
    print("async def process_with_batching():")
    print("    results = await process_documents_from_folder(")
    print("        folder_path='/path/to/documents',")
    print("        n_workers=20,")
    print("        batch_size=100,")
    print("        mongodb_batch_size=200")
    print("    )")
    print("    return results")
    print()
    print("asyncio.run(process_with_batching())")
    print("```")
    print()
    print("Performance Tips:")
    print("• Increase batch sizes for better throughput")
    print("• More workers = faster processing (up to system limits)")
    print("• Monitor system resources (CPU, memory, network)")
    print("• ChromaDB batch size affects embedding API calls")
    print("• MongoDB batch size affects database insertion speed")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--full-test":
            asyncio.run(test_batch_performance())
        elif sys.argv[1] == "--single-test":
            asyncio.run(test_single_configuration())
        elif sys.argv[1] == "--examples":
            print_usage_examples()
        else:
            print("Usage:")
            print("  python test_batch_performance.py --full-test     # Run full performance comparison")
            print("  python test_batch_performance.py --single-test   # Run single optimized test")
            print("  python test_batch_performance.py --examples      # Show usage examples")
    else:
        print("🚀 Batch Processing Performance Tester")
        print()
        print("Choose an option:")
        print("1. Full performance comparison test")
        print("2. Single optimized configuration test")
        print("3. Show usage examples")
        print()
        choice = input("Enter choice (1-3): ").strip()
        choice = "1"
        
        if choice == "1":
            asyncio.run(test_batch_performance())
        elif choice == "2":
            asyncio.run(test_single_configuration())
        elif choice == "3":
            print_usage_examples()
        else:
            print("Invalid choice. Please run with --help for options.")
