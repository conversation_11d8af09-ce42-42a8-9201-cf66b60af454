{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON><PERSON><PERSON>", "Gender": "", "BirthDate": "", "Email": "<EMAIL>", "Address": "Ahmedabad,India", "ContactNumber": "9537127397", "LinkedInProfile": ""}, "Objective": "", "Education": [{"Degree": "CMA FINAL PURSUING GROUP 3 ", "Institution": "ICMAI", "GraduationYear": "Present", "GPA/Marks/%": ""}, {"Degree": "CMA FINAL GROUP 4 -PASS", "Institution": "ICMAI", "GraduationYear": "December 2022", "GPA/Marks/%": "210 OUT OF 400 MARKS"}, {"Degree": "<PERSON><PERSON>-PASS ", "Institution": "GUJARAT UNIVERSITY", "GraduationYear": "April 2022", "GPA/Marks/%": "67%"}, {"Degree": "CMA INTERMEDIATE PASS ", "Institution": "ICMAI", "GraduationYear": "December 2022", "GPA/Marks/%": "537 MARKS OUT OF 800"}, {"Degree": "B.COM-PASS ", "Institution": "GUJARAT UNIVERSITY", "GraduationYear": "April 2021", "GPA/Marks/%": "66.67%"}, {"Degree": "CMA FOUNDATION - PASS", "Institution": "ICMAI", "GraduationYear": "December 2019", "GPA/Marks/%": "301 MARKS OUT OF 400 MARKS"}], "WorkExperience": [{"CompanyName": "Bharat Prajapati and Company", "Role": "JUNIOR ACCOUNTANT", "StartYear": "January 2022", "EndYear": "December 2023", "Description/Responsibility": "Worked majorly in accounting and bookkeeping\nHandled GST returns and reconciliation with Tally data\nManaged costing and cost bookkeeping\nDeveloped and maintained accounting systems and procedures for accurate financial reporting"}, {"CompanyName": "<PERSON><PERSON> abd <PERSON>", "Role": "ARTICLE", "StartYear": "April", "EndYear": "October 2021", "Description/Responsibility": "Conducted 6 months of training in cost accounting and cost records department\n Completed costing for specific clients\nConducted audits,reviews,and compilations for various clients\nReveiwed and analyzed financial data to provide insights into the company's financial performance"}], "Skills": ["Tally Prime Computer Literacy Communication\n Analytical Skills Bookkeeping Accounting Software\nFinancial Analysis"], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": ["Hindi English Gujarati "], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}