/**
 * Formats text to proper case (Title Case)
 * @param {string} text - The text to format
 * @returns {string} - Formatted text in proper case
 */
export const toProperCase = (text) => {
  if (!text || typeof text !== 'string') return text;
  
  // Handle common abbreviations and special cases
  const specialCases = {
    'ai': 'AI',
    'api': 'API',
    'ui': 'UI',
    'ux': 'UX',
    'it': 'IT',
    'hr': 'HR',
    'ceo': 'CEO',
    'cto': 'CTO',
    'cfo': 'CFO',
    'phd': 'PhD',
    'mba': 'MBA',
    'usa': 'USA',
    'uk': 'UK',
    'ios': 'iOS',
    'android': 'Android',
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'nodejs': 'Node.js',
    'reactjs': 'React.js',
    'angularjs': 'Angular.js',
    'vuejs': 'Vue.js',
    'mysql': 'MySQL',
    'postgresql': 'PostgreSQL',
    'mongodb': 'MongoDB',
    'aws': 'AWS',
    'gcp': 'GCP',
    'azure': 'Azure',
    'docker': 'Docker',
    'kubernetes': 'Kubernetes',
    'devops': 'DevOps',
    'fullstack': 'Full-Stack',
    'frontend': 'Front-End',
    'backend': 'Back-End',
    'html': 'HTML',
    'css': 'CSS',
    'sql': 'SQL',
    'json': 'JSON',
    'xml': 'XML',
    'rest': 'REST',
    'graphql': 'GraphQL',
    'saas': 'SaaS',
    'paas': 'PaaS',
    'iaas': 'IaaS'
  };
  
  // Convert to lowercase first, then apply proper case
  return text
    .toLowerCase()
    .split(' ')
    .map(word => {
      // Remove punctuation for checking special cases
      const cleanWord = word.replace(/[^\w]/g, '');
      
      // Check if it's a special case
      if (specialCases[cleanWord]) {
        return word.replace(cleanWord, specialCases[cleanWord]);
      }
      
      // Regular title case
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
};

/**
 * Formats name to proper case with special handling
 * @param {string} name - The name to format
 * @returns {string} - Formatted name
 */
export const formatName = (text) => {
  if (!text || typeof text !== 'string') return text;
  
  // Handle names with special prefixes
  const namePatterns = [
    { pattern: /\bmac/gi, replacement: 'Mac' },
    { pattern: /\bmc/gi, replacement: 'Mc' },
    { pattern: /\bo'/gi, replacement: "O'" },
    { pattern: /\bvan\s/gi, replacement: 'van ' },
    { pattern: /\bde\s/gi, replacement: 'de ' },
    { pattern: /\bla\s/gi, replacement: 'la ' }
  ];
  
  let formatted = toProperCase(text);
  
  // Apply name-specific patterns
  namePatterns.forEach(({ pattern, replacement }) => {
    formatted = formatted.replace(pattern, replacement);
  });
  
  return formatted;
};

/**
 * Formats job title to proper case
 * @param {string} title - The job title to format
 * @returns {string} - Formatted job title
 */
export const formatJobTitle = (text) => {
  if (!text || typeof text !== 'string') return text;
  
  // Common job title patterns
  const titlePatterns = [
    { pattern: /\bsr\b/gi, replacement: 'Sr.' },
    { pattern: /\bjr\b/gi, replacement: 'Jr.' },
    { pattern: /\biii\b/gi, replacement: 'III' },
    { pattern: /\bii\b/gi, replacement: 'II' },
    { pattern: /\biv\b/gi, replacement: 'IV' }
  ];
  
  let formatted = toProperCase(text);
  
  // Apply title-specific patterns
  titlePatterns.forEach(({ pattern, replacement }) => {
    formatted = formatted.replace(pattern, replacement);
  });
  
  return formatted;
};

/**
 * Formats education degree to proper case
 * @param {string} degree - The degree to format
 * @returns {string} - Formatted degree
 */
export const formatDegree = (text) => {
  if (!text || typeof text !== 'string') return text;
  
  // Common degree abbreviations
  const degreePatterns = [
    { pattern: /\bb\.?s\.?\b/gi, replacement: 'B.S.' },
    { pattern: /\bb\.?a\.?\b/gi, replacement: 'B.A.' },
    { pattern: /\bb\.?tech\.?\b/gi, replacement: 'B.Tech' },
    { pattern: /\bb\.?e\.?\b/gi, replacement: 'B.E.' },
    { pattern: /\bm\.?s\.?\b/gi, replacement: 'M.S.' },
    { pattern: /\bm\.?a\.?\b/gi, replacement: 'M.A.' },
    { pattern: /\bm\.?tech\.?\b/gi, replacement: 'M.Tech' },
    { pattern: /\bm\.?e\.?\b/gi, replacement: 'M.E.' },
    { pattern: /\bph\.?d\.?\b/gi, replacement: 'Ph.D.' },
    { pattern: /\bmba\b/gi, replacement: 'MBA' },
    { pattern: /\bmca\b/gi, replacement: 'MCA' },
    { pattern: /\bbca\b/gi, replacement: 'BCA' }
  ];
  
  let formatted = toProperCase(text);
  
  // Apply degree-specific patterns
  degreePatterns.forEach(({ pattern, replacement }) => {
    formatted = formatted.replace(pattern, replacement);
  });
  
  return formatted;
};
