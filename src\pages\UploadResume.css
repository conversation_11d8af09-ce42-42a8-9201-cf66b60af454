.upload-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
  animation: fadeIn 0.5s ease-in;
  min-height: calc(100vh - 80px);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.page-header {
  margin-bottom: 0;
  width: 100%;
  padding-top: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 16px;
  width: 100%;
  position: relative;
}

.header-text {
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 72px; /* Account for settings button width */
}

.settings-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 10px;
  padding: 14px 16px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-width: 48px;
  height: 48px;
}

.settings-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--border-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.settings-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-panel {
  margin: 24px 0;
  padding: 24px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  animation: slideDown 0.3s ease;
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-panel h3 {
  color: var(--text-primary);
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 600;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 8px;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.worker-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--bg-tertiary);
  outline: none;
  margin: 8px 0;
  cursor: pointer;
}

.worker-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.worker-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.worker-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-primary);
  cursor: pointer;
  border: none;
}

.setting-description {
  color: var(--text-tertiary);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

.header-text h1 {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 auto 12px auto;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  text-align: center;
}



.upload-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 100%;
  margin-top: 24px;
}

/* Description Section */
.description-section {
  margin-top: 32px;
  text-align: center;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
}

.description-section p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Upload Section */
.upload-section {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
}

.dropzone {
  padding: 48px 32px;
  border: 2px dashed var(--border-secondary);
  border-radius: 16px;
  background: var(--bg-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  margin: 0;
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.dropzone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(29, 78, 216, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dropzone:hover,
.dropzone.active {
  border-color: var(--accent-primary);
  background: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dropzone:hover::before,
.dropzone.active::before {
  opacity: 1;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.upload-icon {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.dropzone:hover .upload-icon {
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(59, 130, 246, 0.4);
}

.dropzone-content h3 {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.dropzone-content p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  text-align: center;
}

.upload-specs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  font-size: 14px;
  color: var(--text-tertiary);
  margin-top: 20px;
  width: 100%;
  max-width: 500px;
}

.spec-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.spec-item:hover {
  background: var(--bg-primary);
  border-color: var(--border-secondary);
}

.upload-actions {
  padding: 20px 32px;
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  align-items: center;
}

/* File List Section */
.file-list-section {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.section-header {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-tertiary);
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.file-list {
  padding: 0;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 32px;
  border-bottom: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: var(--bg-tertiary);
}

.file-item.success {
  background: var(--bg-secondary);
  border-left: 3px solid var(--success);
}

.file-item.error {
  background: var(--bg-secondary);
  border-left: 3px solid var(--error);
}

.file-item.uploading {
  background: var(--bg-secondary);
  border-left: 3px solid var(--accent-primary);
}

.file-item.skipped {
  background: var(--bg-secondary);
  border-left: 3px solid var(--warning);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.file-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.file-item.success .file-icon {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--success);
}

.file-item.error .file-icon {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--error);
}

.file-item.uploading .file-icon {
  background: rgba(59, 130, 246, 0.1);
  border-color: var(--accent-primary);
}

.file-item.skipped .file-icon {
  background: rgba(245, 158, 11, 0.1);
  border-color: var(--warning);
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon.pending {
  color: var(--text-secondary);
}

.status-icon.success {
  color: var(--success);
}

.status-icon.error {
  color: var(--error);
}

.status-icon.warning {
  color: var(--warning);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  word-break: break-word;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.error-text {
  color: var(--error);
}

.warning-text {
  color: var(--warning);
}

.success-text {
  color: var(--success);
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 120px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 35px;
  text-align: right;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: var(--bg-tertiary);
  color: var(--error);
}

.success-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.view-btn {
  background: var(--accent-primary);
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.view-btn:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.view-btn:disabled {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--bg-tertiary);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-page {
    padding: 16px;
    max-width: 100%;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;
  }

  .header-text {
    width: 100%;
    text-align: center;
    padding-right: 0; /* Remove padding on mobile */
  }

  .header-text h1 {
    font-size: 28px;
  }

  .settings-btn {
    align-self: center;
    width: fit-content;
    margin-top: 8px;
  }

  .upload-container {
    margin-top: 16px; /* Reduce margin on mobile */
  }

  .description-section {
    margin-top: 24px;
    padding: 20px;
  }

  .description-section p {
    font-size: 15px;
  }

  .dropzone {
    padding: 32px 20px;
  }

  .upload-icon {
    width: 60px;
    height: 60px;
  }

  .dropzone-content h3 {
    font-size: 18px;
  }

  .dropzone-content p {
    font-size: 14px;
  }

  .upload-specs {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .upload-actions {
    padding: 20px 24px;
    flex-direction: column;
  }

  .section-header {
    padding: 20px 24px;
  }

  .file-item {
    padding: 16px 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .file-info {
    width: 100%;
  }

  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .progress-container {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .upload-page {
    padding: 12px;
  }

  .header-text h1 {
    font-size: 24px;
  }

  .dropzone {
    padding: 24px 16px;
  }

  .upload-icon {
    width: 56px;
    height: 56px;
  }

  .dropzone-content h3 {
    font-size: 16px;
  }

  .upload-specs {
    gap: 6px;
    margin-top: 16px;
  }

  .spec-item {
    font-size: 13px;
    padding: 6px 10px;
  }

  .upload-actions {
    padding: 16px 20px;
  }

  .section-header {
    padding: 16px 20px;
  }

  .file-item {
    padding: 12px 20px;
  }

  .settings-panel {
    padding: 20px;
    margin-top: 20px;
    width: 100%;
    box-sizing: border-box;
  }

  .description-section {
    margin-top: 20px;
    padding: 16px;
  }

  .description-section p {
    font-size: 14px;
  }
}
