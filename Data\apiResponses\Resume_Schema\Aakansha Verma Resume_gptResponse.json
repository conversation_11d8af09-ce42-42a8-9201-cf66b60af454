{"id": "chatcmpl-BegH9T2oS9xUHikQqpwhAAQAsr4Lo", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"Aakansha Verma\",\"Gender\":\"Female\",\"BirthDate\":19980707,\"Email\":\"<EMAIL>\",\"Address\":\"171/A, Ashok Nagar, Airport Road, Indore, 452005 Madhya Pradesh India\",\"ContactNumber\":\"9617134278\",\"LinkedInProfile\":\"\"},\"Objective\":\"To follow passion in teaching to inspire and nurture students in developing the character and competence with modern education.\",\"Education\":[{\"Degree\":\"B.Ed.\",\"Institution\":\"School of Education, DAVV\",\"GraduationYear\":\"2023\",\"GPA/Marks/%\":89.2},{\"Degree\":\"B.C.A. (Computer Application)\",\"Institution\":\"Shri Cloth Market Institute of Professional Studies, DAVV\",\"GraduationYear\":\"2017\",\"GPA/Marks/%\":75},{\"Degree\":\"Higher Secondary Education\",\"Institution\":\"Shri Cloth Market Kanya Vidhyalaya\",\"GraduationYear\":\"2014\",\"GPA/Marks/%\":88},{\"Degree\":\"Secondary Education\",\"Institution\":\"Shri Cloth Market Kanya Vidhyalaya\",\"GraduationYear\":\"2012\",\"GPA/Marks/%\":85}],\"WorkExperience\":[{\"CompanyName\":\"Ashashkiya Sanyogitaganj Higher Secondary School\",\"Role\":\"Internship\",\"StartYear\":62023,\"EndYear\":122023,\"Description/Responsibility\":\"\"}],\"TotalWorkExperienceInYears\":0.5,\"Skills\":[\"Unit and Lesson Planning\",\"Good knowledge of computer (MS Office)\",\"Effective Evaluation Planning\",\"Computer Based Resource Development\"],\"Certifications\":[{\"CertificationName\":\"MPTET Varg 2 (Pre Exam Qualified)\",\"IssuingOrganization\":\"\",\"IssueDate\":2023,\"ExpiryDate\":0},{\"CertificationName\":\"Central Teacher Eligibility Test (CTET)\",\"IssuingOrganization\":\"\",\"IssueDate\":2022,\"ExpiryDate\":0},{\"CertificationName\":\"CGL 2020 (Pre Exam Qualified)\",\"IssuingOrganization\":\"\",\"IssueDate\":2020,\"ExpiryDate\":0},{\"CertificationName\":\"DOEACC o Level Computer Diploma (NIELIT)\",\"IssuingOrganization\":\"\",\"IssueDate\":2018,\"ExpiryDate\":0}],\"Achievements\":[],\"Languages\":[\"Hindi\",\"English\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "annotations": []}}], "created": 1749035327, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_07871e2ad8", "usage": {"completion_tokens": 457, "prompt_tokens": 2788, "total_tokens": 3245, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 2688}}}