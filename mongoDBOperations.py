#!/usr/bin/env python3
"""
Example usage of mongoDBInsertion.py

This script demonstrates how to use the DocumentProcessor class
to process documents from a folder and store them in MongoDB.
"""

import asyncio
import os
from mongoDBInsertion import DocumentProcessor, process_documents_from_folder

async def example_basic_usage():
    """Basic example of processing documents from a folder with parallel processing."""

    # Example folder path (replace with your actual folder path)
    folder_path = r"H:\AI Data\21_ResumesByMsKopalJain\New folder"

    # Create the folder and add some example files if it doesn't exist
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        print(f"Created example folder: {folder_path}")
        print("Please add some PDF, DOC, DOCX, or image files to this folder and run again.")
        return

    print("=== Basic Usage Example (Parallel Processing) ===")
    print(f"Processing documents from: {folder_path}")

    # Process documents using the convenience function with default 12 workers
    results = await process_documents_from_folder(
        folder_path=folder_path,
        database_name="dbProductionV2",  # Default database
        collection_name="collectionResumeV2",  # Default collection
        n_workers=12  # Default parallel workers
    )
    
    # Print results
    print("\n=== Processing Results ===")
    print(f"Total files found: {results.get('total_files', 0)}")
    print(f"Successfully processed: {results.get('processed_successfully', 0)}")
    print(f"Failed: {results.get('failed_files', 0)}")
    print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
    print(f"Processing time: {results.get('processing_time', 'N/A')}")
    
    if results.get('processed_files'):
        print(f"\nSuccessfully processed files:")
        for filename in results['processed_files']:
            print(f"  ✓ {filename}")
    
    if results.get('failed_files_list'):
        print(f"\nFailed files:")
        for filename in results['failed_files_list']:
            print(f"  ✗ {filename}")
    
    if results.get('duplicate_files_list'):
        print(f"\nDuplicate files (skipped):")
        for filename in results['duplicate_files_list']:
            print(f"  ⚠ {filename}")


async def example_custom_settings():
    """Example with custom database and collection settings."""

    folder_path = "./sample_documents"

    if not os.path.exists(folder_path):
        print(f"Folder {folder_path} does not exist. Please create it and add some files.")
        return

    print("\n=== Custom Settings Example ===")

    # Create processor with custom settings
    processor = DocumentProcessor(
        database_name="my_custom_database",
        collection_name="my_custom_collection",
        vendor_name="Resume_Schema",  # This should match your config
        n_workers=8  # Custom worker count
    )

    # Process the folder
    results = await processor.process_folder(folder_path)

    print(f"Processed with custom database: my_custom_database")
    print(f"Collection: my_custom_collection")
    print(f"Workers: {results.get('n_workers', 'N/A')}")
    print(f"Results: {results.get('processed_successfully', 0)} successful, {results.get('failed_files', 0)} failed")


async def example_high_performance_processing():
    """Example of high-performance processing for large document sets."""

    folder_path = "./sample_documents"

    if not os.path.exists(folder_path):
        print(f"Folder {folder_path} does not exist. Please create it and add some files.")
        return

    print("\n=== High-Performance Processing Example ===")
    print("Optimized for processing 200+ files with maximum parallelism")

    # Use maximum workers for high-performance processing
    results = await process_documents_from_folder(
        folder_path=folder_path,
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        n_workers=20  # High worker count for large datasets
    )

    print(f"\n=== High-Performance Results ===")
    print(f"Workers used: {results.get('n_workers', 'N/A')}")
    print(f"Total files: {results.get('total_files', 0)}")
    print(f"Successfully processed: {results.get('processed_successfully', 0)}")
    print(f"Failed: {results.get('failed_files', 0)}")
    print(f"Duplicates skipped: {results.get('duplicate_files', 0)}")
    print(f"Processing time: {results.get('processing_time', 'N/A')}")

    # Calculate and display performance metrics
    if results.get('processed_successfully', 0) > 0:
        from datetime import datetime
        start_time = results.get('start_time')
        end_time = results.get('end_time')
        if start_time and end_time:
            total_seconds = (end_time - start_time).total_seconds()
            throughput = results['processed_successfully'] / total_seconds if total_seconds > 0 else 0
            print(f"Throughput: {throughput:.2f} files/second")
            print(f"Average time per file: {total_seconds/results['processed_successfully']:.2f} seconds")

    if results.get('failed_files_list'):
        print(f"\nFailed files: {', '.join(results['failed_files_list'][:5])}")
        if len(results['failed_files_list']) > 5:
            print(f"... and {len(results['failed_files_list']) - 5} more")


async def example_single_file_processing():
    """Example of processing a single file."""
    
    # Example file path (replace with your actual file)
    file_path = "./sample_documents/example.pdf"
    
    if not os.path.exists(file_path):
        print(f"File {file_path} does not exist. Please provide a valid file path.")
        return
    
    print("\n=== Single File Processing Example ===")
    
    processor = DocumentProcessor()
    
    # Create a temporary directory for processing
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        success = await processor.process_single_file(file_path, temp_dir)
        
        if success:
            print(f"✓ Successfully processed: {file_path}")
        else:
            print(f"✗ Failed to process: {file_path}")


def example_list_documents():
    """Example of listing processed documents."""
    
    print("\n=== List Documents Example ===")
    
    from mongoDBInsertion import list_processed_documents
    
    # List documents from default database/collection
    documents = list_processed_documents()
    
    if documents:
        print(f"Found {len(documents)} processed documents:")
        print("-" * 60)
        for doc in documents[:5]:  # Show first 5 documents
            print(f"ID: {doc['_id']}")
            print(f"Filename: {doc.get('original_filename', 'N/A')}")
            print(f"Processed: {doc.get('timestamp', 'N/A')}")
            print(f"PDF File ID: {doc.get('pdf_file_id', 'N/A')}")
            print("-" * 60)
        
        if len(documents) > 5:
            print(f"... and {len(documents) - 5} more documents")
    else:
        print("No processed documents found.")


def example_retrieve_file():
    """Example of retrieving a file from GridFS."""
    
    print("\n=== Retrieve File Example ===")
    
    from mongoDBInsertion import list_processed_documents, get_file_from_gridfs
    from bson import ObjectId
    
    # Get a document with a PDF file
    documents = list_processed_documents()
    
    if not documents:
        print("No documents found to retrieve.")
        return
    
    # Find a document with a PDF file ID
    doc_with_pdf = None
    for doc in documents:
        if doc.get('pdf_file_id'):
            doc_with_pdf = doc
            break
    
    if not doc_with_pdf:
        print("No documents with PDF files found.")
        return
    
    # Retrieve the file
    pdf_file_id = doc_with_pdf['pdf_file_id']
    output_path = f"retrieved_{doc_with_pdf.get('original_filename', 'document.pdf')}"
    
    success = get_file_from_gridfs(
        database_name="dbProductionV2",
        file_id=pdf_file_id,
        output_path=output_path
    )
    
    if success:
        print(f"✓ File retrieved successfully: {output_path}")
    else:
        print("✗ Failed to retrieve file")


async def main():
    """Main function to run all examples."""

    print("MongoDB Document Insertion - Usage Examples (Parallel Processing)")
    print("=" * 60)

    try:
        # Run basic usage example
        # await example_basic_usage()

        # # Run custom settings example
        # await example_custom_settings()

        # # Run high-performance processing example
        # await example_high_performance_processing()

        # # Run single file processing example
        # await example_single_file_processing()

        # # List documents example (synchronous)
        example_list_documents()

        # # Retrieve file example (synchronous)
        # example_retrieve_file()

    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
