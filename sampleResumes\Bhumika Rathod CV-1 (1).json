{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON><PERSON>", "Gender": "Female", "BirthDate": "08 January, 2005 ", "Email": "<EMAIL>", "Address": "", "ContactNumber": "6201842344", "LinkedInProfile": ""}, "Objective": "Seeking a working environment that allows me to leverage my existing experience as well as\n Interpersonal skills, which provides opportunities for increased learning and career\n advancement. A working environment that not only helps me to grow personally and\n professionally, but also to firmly contribute towards the achievement of the mission and\n values of the organization. ", "Education": [{"Degree": "ACCA F4-Business law \n(Knowledge level)", "Institution": "Association of \n chartered certified \naccountants", "GraduationYear": "February -2023", "GPA/Marks/%": "50% (50/100) "}, {"Degree": "ACCA F3-\n Management \nAccounting\n (Knowledge level) ", "Institution": "Association of \n chartered certified \naccountants", "GraduationYear": "December-2022  ", "GPA/Marks/%": "64% (64/100) "}, {"Degree": "ACCA F2-Business\nTechnology\n(Knowledge level)", "Institution": "Association of \n chartered certified \naccountants", "GraduationYear": "October -2022 ", "GPA/Marks/%": "65% (65/100)  "}, {"Degree": "ACCA F1-Financial \n Accounting\n(Knowledge level)", "Institution": "Association of \n chartered certified \naccountants", "GraduationYear": "September-2022 ", "GPA/Marks/%": "67% (67/100)"}], "WorkExperience": [{"CompanyName": "ADAS Globus KPO.", "Role": "Virtual Assistant of Australia Division", "StartYear": "1 May 2023", "EndYear": "", "Description/Responsibility": "Monthly Bank entries for particular site work going on at Australia.\n  Reconciliation of Bank statement. Invoice entries at google Sheet  "}], "Skills": ["Basics of QuickBooks Online & Xero for Accounting. \nConversant in Microsoft Word, Excel, Power Point and Share point. "], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": " 'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": ["English, Hindi, and Gujarati. "], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}