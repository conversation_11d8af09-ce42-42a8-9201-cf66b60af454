{"id": "chatcmpl-BeLTNqOyChHoPXuE7HCpGzhlYOmcA", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"<PERSON><PERSON><PERSON> L <PERSON>\",\"Gender\":\"Female\",\"BirthDate\":19970529,\"Email\":\"<EMAIL>\",\"Address\":\"B/H Vishnu Complex, Chandekar area, C. A. Road Nagpur-440008\",\"ContactNumber\":\"9975995768\",\"LinkedInProfile\":\"\"},\"Objective\":\"To get an opportunity where I can make the best of my potential and contribute to the organization's growth.\",\"Education\":[{\"Degree\":\"Master of Business Administration\",\"Institution\":\"RTM Nagpur University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"BE in Electronics & Telecommunication Engineering\",\"Institution\":\"Savitri bai Phule University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Diploma in Electronic Engineering\",\"Institution\":\"MSBTE\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Xth\",\"Institution\":\"Maharashtra State Board\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"JAFS Engineering service PVT. LTD.\",\"Role\":\"Intern\",\"StartYear\":2017,\"EndYear\":2017,\"Description/Responsibility\":\"Get exposure to practical knowledge of working industry in CCTV and Networking department\"},{\"CompanyName\":\"BSNL RTTC NAGPUR\",\"Role\":\"Intern\",\"StartYear\":2015,\"EndYear\":2015,\"Description/Responsibility\":\"Get to know various aspect of Telecommunication\"},{\"CompanyName\":\"MTA's DIGTECH\",\"Role\":\"Intern\",\"StartYear\":2014,\"EndYear\":2014,\"Description/Responsibility\":\"Ethical Hacking and Networking Security\"}],\"TotalWorkExperienceInYears\":0,\"Skills\":[\"MS Suite (Excel, Power Point, Word, Outlook)\",\"Verbal Communication\",\"Time Management\",\"Critical thinking\",\"Basic knowledge C and Python Language\"],\"Certifications\":[{\"CertificationName\":\"MSCIT\",\"IssuingOrganization\":\"\",\"IssueDate\":0,\"ExpiryDate\":0}],\"Achievements\":[{\"AchievementName\":\"1st prize in Skit Competition\",\"IssuingOrganization\":\"Green Heaven Institute of Research and Management\",\"IssueDate\":0},{\"AchievementName\":\"2nd prize at National Level Project Competition\",\"IssuingOrganization\":\"PAROKSHA\",\"IssueDate\":0}],\"Languages\":[\"Marathi\",\"Hindi\",\"English\"],\"Projects\":[{\"ProjectName\":\"Smart Watch for Senior/Elderly Project Using Microcontroller\",\"Description\":\"\",\"TechnologiesUsed\":[\"Arduino IDE\",\"Proteus Simulation\"],\"Role\":\"Team Lead, presentation, fabrication\"},{\"ProjectName\":\"Dual Power Supply Trainer Kit- Mini Project\",\"Description\":\"\",\"TechnologiesUsed\":[\"Proteus Simulation\"],\"Role\":\"Marketing Survey, soldering of components, real time implementation\"},{\"ProjectName\":\"GPS Based Soldier Tracking and Health Monitoring System\",\"Description\":\"\",\"TechnologiesUsed\":[\"Proteus Simulation\"],\"Role\":\"Team lead, market survey\"}]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "annotations": []}}], "created": **********, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_a288987b44", "usage": {"completion_tokens": 589, "prompt_tokens": 4238, "total_tokens": 4827, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 1152}}}