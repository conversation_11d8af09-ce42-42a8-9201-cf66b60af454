{"Resume_Schema": {"type": "json_schema", "json_schema": {"name": "Resume_Schema", "strict": true, "schema": {"type": "object", "properties": {"Resume": {"type": "object", "description": "The main container for all resume details.", "properties": {"PersonalInformation": {"type": "object", "description": "Personal details of the individual.", "properties": {"FullName": {"type": "string", "description": "The full name of the individual."}, "FullNameEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for FullName. Leave null when extracting from resume.", "items": {"type": "number"}}, "Gender": {"type": "string", "description": "The gender of the individual. If not explicitly stated, infer it from the individual's name using common naming conventions. Must be one of: Male or Female.", "enum": ["Male", "Female", "Other"]}, "BirthDate": {"type": ["string", "null"], "description": "The birth date of the individual.", "format": "date"}, "Email": {"type": "string", "description": "The email address of the individual.", "format": "email"}, "Address": {"type": "string", "description": "The residential address of the individual."}, "City": {"type": "string", "description": "The city where the individual resides.", "anyOf": [{"enum": ["New York", "Los Angeles", "Chicago", "San Francisco", "Houston", "Miami", "Boston", "Washington D.C.", "London", "Paris", "Berlin", "Madrid", "Rome", "Barcelona", "Amsterdam", "Brussels", "Vienna", "Stockholm", "Copenhagen", "Oslo", "Helsinki", "Zurich", "Tokyo", "Osaka", "Beijing", "Shanghai", "Shenzhen", "Guangzhou", "Seoul", "Hong Kong", "Singapore", "Bangkok", "Jakarta", "Manila", "Dubai", "Abu Dhabi", "Doha", "Riyadh", "Jeddah", "Tehran", "Baghdad", "Cairo", "Lagos", "Nairobi", "Johannesburg", "Cape Town", "São Paulo", "Rio de Janeiro", "Buenos Aires", "Lima", "Bogotá", "Mexico City", "Toronto", "Vancouver", "Montreal", "Sydney", "Melbourne", "Auckland", "Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata", "Pune", "Ahmedabad", "Surat", "Jaipur", "Lucknow", "Kanpur", "Nagpur", "Visakhapatnam", "Indore", "Bhopal", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Agra", "<PERSON><PERSON><PERSON>", "Coimbatore", "Vadodara", "<PERSON><PERSON>", "Faridabad", "<PERSON><PERSON><PERSON>", "Rajkot", "Amritsar", "Allahabad (Prayagraj)", "Ranchi", "Guwahati", "Chandigarh", "Jodhpur", "Madurai", "Raipur", "<PERSON><PERSON>", "Jamshedpur", "Thiruvananthapuram", "Dehradun", "Jabalpur", "Gwalior", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Satna", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Khandwa", "Chhindwara", "<PERSON><PERSON><PERSON><PERSON>", "Shivpuri", "<PERSON><PERSON>", "<PERSON><PERSON>", "Hoshangabad", "<PERSON><PERSON><PERSON>", "Betul", "Se<PERSON>"]}, {"pattern": ".*"}]}, "State": {"type": "string", "description": "The state where the individual resides.", "anyOf": [{"enum": ["California", "Texas", "Florida", "New York", "Illinois", "Pennsylvania", "Ohio", "Uttar Pradesh", "Maharashtra", "Bihar", "West Bengal", "Tamil Nadu", "Karnataka", "Gujarat", "Rajasthan", "Madhya Pradesh", "Andhra Pradesh", "Kerala", "Odisha", "Punjab", "Haryana", "Telangana", "Assam", "Chhattisgarh", "Jharkhand", "Guangdong", "Shandong", "<PERSON><PERSON>", "Sichuan", "Jiangsu", "Hebei", "Hunan", "<PERSON><PERSON>", "Zhejiang", "Yunnan", "Beijing", "Shanghai", "Ontario", "Quebec", "British Columbia", "Alberta", "New South Wales", "Victoria", "Queensland", "Western Australia", "São Paulo", "Minas Gerais", "Rio de Janeiro", "Bahia", "Paraná", "Moscow", "Saint Petersburg", "Krasnodar Krai", "Tatarstan", "Sverdlovsk Oblast", "Bavaria", "North Rhine-Westphalia", "Baden-Württemberg", "Berlin", "Hesse", "Mexico City", "Jalisco", "Nuevo León", "Estado de México", "Gauteng", "KwaZulu-Natal", "Western Cape", "Lagos", "<PERSON><PERSON>", "Ka<PERSON><PERSON>", "Rivers", "Java", "Sumatra", "Bali", "Kalimantan", "England", "Scotland", "Wales", "Northern Ireland"]}, {"pattern": ".*"}]}, "ContactNumber": {"type": "string", "description": "The contact phone number."}, "LinkedInProfile": {"type": "string", "description": "URL to the individual's LinkedIn profile."}}, "additionalProperties": false, "required": ["FullName", "FullNameEmbedding", "Gender", "BirthDate", "Email", "Address", "City", "State", "ContactNumber", "LinkedInProfile"]}, "Objective": {"type": "string", "description": "A career objective or summary statement."}, "Education": {"type": "array", "description": "A list of educational qualifications.", "items": {"type": "object", "description": "An educational qualification entry.", "properties": {"Degree": {"type": "string", "description": "The degree or qualification earned. Try to match the list, else allow fallback.", "anyOf": [{"enum": ["10th Pass", "12th Pass", "BA (Bachelor of Arts)", "BSc (Bachelor of Science)", "<PERSON><PERSON> (Bachelor of Commerce)", "B<PERSON> (Bachelor of Business Administration)", "BCA (Bachelor of Computer Applications)", "<PERSON><PERSON> (Bachelor of Technology)", "<PERSON><PERSON> (Bachelor of Engineering)", "<PERSON><PERSON> (Bachelor of Laws)", "MBBS (Bachelor of Medicine and Bachelor of Surgery)", "<PERSON><PERSON><PERSON> (Bachelor of Pharmacy)", "<PERSON><PERSON> (Bachelor of Architecture)", "BDS (Bachelor of Dental Surgery)", "BFA (Bachelor of Fine Arts)", "<PERSON><PERSON><PERSON> (Bachelor of Hotel Management)", "BPT (Bachelor of Physiotherapy)", "BSc IT (Bachelor of Science in Information Technology)", "<PERSON><PERSON><PERSON> (Bachelor of Design)", "<PERSON><PERSON> (Bachelor of Mass Media)", "BJ<PERSON> (Bachelor of Journalism and Mass Communication)", "<PERSON><PERSON><PERSON> (Bachelor of Library Science)", "BPEd (Bachelor of Physical Education)", "BSc Nursing (Bachelor of Science in Nursing)", "BEd (Bachelor of Education)", "BA LLB (Bachelor of Arts and Bachelor of Laws)", "BBA LLB (Bachelor of Business Administration and Bachelor of Laws)", "MA (Master of Arts)", "MSc (Master of Science)", "<PERSON><PERSON> (Master of Commerce)", "MBA (Master of Business Administration)", "MCA (Master of Computer Applications)", "<PERSON><PERSON><PERSON> (Master of Technology)", "<PERSON> (Master of Engineering)", "<PERSON><PERSON> (Master of Laws)", "<PERSON><PERSON><PERSON> (Master of Pharmacy)", "<PERSON><PERSON> (Master of Fine Arts)", "MD<PERSON> (Master of Dental Surgery)", "<PERSON><PERSON><PERSON> (Master of Hotel Management)", "MP<PERSON> (Master of Physiotherapy)", "MS (Master of Surgery)", "<PERSON><PERSON><PERSON> (Master of Library Science)", "MPEd (Master of Physical Education)", "MSc Nursing (Master of Science in Nursing)", "MEd (Master of Education)", "MJ<PERSON> (Master of Journalism and Mass Communication)", "<PERSON><PERSON> (Master of Design)", "PhD (Doctor of Philosophy)", "<PERSON><PERSON> (Doctor of Science)", "<PERSON><PERSON> (Doctor of Literature)", "BEd (Bachelor of Education)", "MEd (Master of Education)", "D.El.Ed (Diploma in Elementary Education)", "B.El.Ed (Bachelor of Elementary Education)", "NTT (Nursery Teacher Training)", "PTT (Primary Teacher Training)", "TTC (Teacher Training Certificate)", "CTET (Central Teacher Eligibility Test)", "TET (Teacher Eligibility Test)", "UPTET (Uttar Pradesh Teacher Eligibility Test)", "MPTET (Madhya Pradesh Teacher Eligibility Test)", "HTET (Haryana Teacher Eligibility Test)", "KTET (Kerala Teacher Eligibility Test)", "OTET (Odisha Teacher Eligibility Test)", "AP TET (Andhra Pradesh Teacher Eligibility Test)", "KVS Exam (Kendriya Vidyalaya Sangathan Recruitment)", "NVS Exam (Navodaya Vidyalaya Samiti Recruitment)", "STET (State Teacher Eligibility Test)", "UGC NET (University Grants Commission National Eligibility Test)", "SET (State Eligibility Test)", "IGNOU BEd (Indira Gandhi National Open University BEd Program)", "IGNOU MEd (Indira Gandhi National Open University MEd Program)", "PGCE (Postgraduate Certificate in Education - UK)", "TEFL (Teaching English as a Foreign Language)", "TESOL (Teaching English to Speakers of Other Languages)", "CELTA (Certificate in English Language Teaching to Adults)", "Montessori Training (Early Childhood Education Certification)", "IB Certification (International Baccalaureate Teacher Training)", "WBT (Waldorf Basic Training)", "DELTA (Diploma in Teaching English to Speakers of Other Languages - Cambridge)", "CA (Chartered Accountant)", "CS (Company Secretary)", "CMA (Cost and Management Accountant)", "CPA (Certified Public Accountant)", "CFA (Chartered Financial Analyst)", "ITI (Industrial Training Institute)", "DCA (Diploma in Computer Applications)", "PGDCA (Post Graduate Diploma in Computer Applications)", "<PERSON><PERSON><PERSON><PERSON> (Diploma in Pharmacy)", "GNM (General Nursing and Midwifery)", "ANM (Auxiliary Nurse Midwifery)", "DOEACC O Level (Foundation Diploma in Computer Science)", "DOEACC A Level (Advanced Diploma in Computer Applications)", "<PERSON><PERSON> (Bachelor of Statistics)", "<PERSON><PERSON> (Master of Statistics)"]}, {"pattern": ".*"}]}, "Specialization": {"type": "string", "description": "The specialization of the degree, if applicable.", "anyOf": [{"enum": ["Artificial Intelligence", "Machine Learning", "Data Science", "Cybersecurity", "Blockchain", "Web Development", "Mobile App Development", "Cloud Computing", "Information Technology", "Software Engineering", "Computer Networks", "Big Data Analytics", "Computer Applications", "Mechanical Engineering", "Civil Engineering", "Electrical Engineering", "Electronics and Communication", "Computer Engineering", "Automobile Engineering", "Mechatronics", "Aerospace Engineering", "Chemical Engineering", "Biotechnology", "Environmental Engineering", "Petroleum Engineering", "Physics", "Chemistry", "Mathematics", "Zoology", "Botany", "Biochemistry", "Microbiology", "Genetics", "Environmental Science", "Forensic Science", "Agricultural Science", "Accounting and Finance", "Marketing", "Human Resource Management", "International Business", "Banking and Insurance", "Business Analytics", "Operations Management", "Entrepreneurship", "Supply Chain Management", "E-Commerce", "English Literature", "History", "Political Science", "Sociology", "Psychology", "Philosophy", "Economics", "Geography", "Public Administration", "Linguistics", "Corporate Law", "Criminal Law", "International Law", "Cyber Law", "Intellectual Property Law", "Environmental Law", "Constitutional Law", "Labour Law", "General Medicine", "Surgery", "Dentistry", "Pharmacy", "Nursing", "Physiotherapy", "Radiology", "Pathology", "Gynecology", "Pediatrics", "Dermatology", "Psychiatry", "Public Health", "Ayurveda", "Homeopathy", "Graphic Design", "Fashion Design", "Interior Design", "UI/UX Design", "Animation and VFX", "Photography", "Film Making", "Mass Communication", "Journalism", "Advertising", "Horticulture", "Forestry", "Dairy Technology", "Fisheries Science", "Agronomy", "Soil Science", "Plant Pathology", "Early Childhood Education", "Elementary Education", "Secondary Education", "Special Education", "Educational Psychology", "Curriculum and Instruction", "Counseling and Guidance", "Educational Leadership and Administration", "Higher Education", "Adult and Continuing Education", "Teacher Training and Pedagogy", "Language Education (English, Hindi, Regional Languages)", "Mathematics Education", "Science Education", "Social Science Education", "Physical Education", "Inclusive Education", "Educational Technology", "Assessment and Evaluation in Education", "Vocational Education", "Moral and Value Education"]}, {"pattern": ".*"}]}, "Institution": {"type": "string", "description": "The full, standardized name of the educational institution. Avoid abbreviations or variants. For example, return 'Sage University' instead of 'SU' or 'Sage Uni'. Remove campus-specific suffixes unless officially part of the name. Ensure consistency across entries for the same institution."}, "InstitutionEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for InstitutionEmbedding. Leave null when extracting from resume.", "items": {"type": "number"}}, "GraduationYear": {"type": ["string", "null"], "description": "The year or date of graduation.", "format": "date"}, "GPA": {"type": "number", "description": "If mentioned, extract GPA value (e.g., GPA of 4.0)"}, "MaxGPA": {"type": "number", "description": "Use your knowledge of the institution or city to infer the GPA scale (e.g., 4.33 for IIM Ahmedabad)"}, "Percentage": {"type": "number", "description": "Convert GPA to percentage using: (GPA / MaxGPA) * 100. If percentage is explicitly mentioned, extract it."}}, "additionalProperties": false, "required": ["Degree", "Specialization", "Institution", "InstitutionEmbedding", "GraduationYear", "GPA", "MaxGPA", "Percentage"]}}, "WorkExperience": {"type": "array", "description": "A list of work experiences.", "items": {"type": "object", "description": "A work experience entry detailing employment information.", "properties": {"CompanyName": {"type": "string", "description": "The name of the company or organization."}, "CompanyNameEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for CompanyName. Leave null when extracting from resume.", "items": {"type": "number"}}, "Role": {"type": "string", "description": "The job title or role held at the company."}, "RoleEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for <PERSON>. Leave null when extracting from resume.", "items": {"type": "number"}}, "StartYear": {"type": ["string", "null"], "description": "The starting year or date of the employment.", "format": "date"}, "EndYear": {"type": ["string", "null"], "description": "The ending year or date of the employment.", "format": "date"}, "Description/Responsibility": {"type": "string", "description": "A description of the responsibilities and tasks performed in the role."}, "Description/ResponsibilityEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for Description/Responsibility. Leave null when extracting from resume.", "items": {"type": "number"}}}, "additionalProperties": false, "required": ["CompanyName", "CompanyNameEmbedding", "Role", "RoleEmbedding", "StartYear", "EndYear", "Description/Responsibility", "Description/ResponsibilityEmbedding"]}}, "TotalWorkExperienceInYears": {"type": "string", "description": "Total work experience in the format 'N Years and N Months'. Example: '5 Years and 3 Months'", "format": "duration"}, "Skills": {"type": "array", "description": "A list of professional skills.", "items": {"type": "string", "description": "A single skill."}}, "SkillsEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for Skills. Leave null when extracting from resume.", "items": {"type": "number"}}, "Certifications": {"type": "array", "description": "A list of professional certifications.", "items": {"type": "object", "description": "A certification detail.", "properties": {"CertificationName": {"type": "string", "description": "The name of the certification."}, "CertificationNameEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for CertificationName. Leave null when extracting from resume.", "items": {"type": "number"}}, "IssuingOrganization": {"type": "string", "description": "The organization that issued the certification."}, "IssuingOrganizationEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for IssuingOrganization. Leave null when extracting from resume.", "items": {"type": "number"}}, "IssueDate": {"type": ["string", "null"], "description": "The date when the certification was issued.", "format": "date"}, "ExpiryDate": {"type": ["string", "null"], "description": "The expiry date of the certification.", "format": "date"}}, "additionalProperties": false, "required": ["CertificationName", "CertificationNameEmbedding", "IssuingOrganization", "IssuingOrganizationEmbedding", "IssueDate", "ExpiryDate"]}}, "Achievements": {"type": "array", "description": "A list of professional achievements or awards.", "items": {"type": "object", "description": "An achievement or award entry.", "properties": {"AchievementName": {"type": "string", "description": "The name or title of the achievement."}, "AchievementNameEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for AchievementName. Leave null when extracting from resume.", "items": {"type": "number"}}, "IssuingOrganization": {"type": "string", "description": "The organization that recognized the achievement."}, "IssuingOrganizationEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for IssuingOrganization. Leave null when extracting from resume.", "items": {"type": "number"}}, "IssueDate": {"type": ["string", "null"], "description": "The date when the achievement was awarded.", "format": "date"}}, "additionalProperties": false, "required": ["AchievementName", "AchievementNameEmbedding", "IssuingOrganization", "IssuingOrganizationEmbedding", "IssueDate"]}}, "Languages": {"type": "array", "description": "A list of languages known by the individual.", "items": {"type": "string", "description": "A single language.", "anyOf": [{"enum": ["English", "Mandarin Chinese", "Spanish", "Arabic", "Portuguese", "Russian", "Japanese", "German", "French", "Turkish", "Korean", "Vietnamese", "Persian (Farsi)", "Malay/Indonesian", "Thai", "Polish", "Ukrainian", "Dutch", "Romanian", "Swahili", "Hindi", "Bengali", "Telugu", "Marathi", "Tamil", "Urdu", "Gujarati", "Kannada", "Odia", "Malayalam", "Punjabi", "Assamese", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kashmiri", "Nepali", "Konkani", "<PERSON><PERSON>", "Manipuri", "Sindhi", "Bodo", "Santhali", "Sanskrit"]}, {"pattern": ".*"}]}}, "Projects": {"type": "array", "description": "A list of projects undertaken by the individual.", "items": {"type": "object", "description": "Details of a single project.", "properties": {"ProjectName": {"type": "string", "description": "The name of the project."}, "ProjectNameEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for ProjectName. Leave null when extracting from resume.", "items": {"type": "number"}}, "Description": {"type": "string", "description": "A brief description of the project."}, "DescriptionEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for Description. Leave null when extracting from resume.", "items": {"type": "number"}}, "TechnologiesUsed": {"type": "array", "description": "A list of technologies used in the project.", "items": {"type": "string", "description": "A technology used in the project."}}, "TechnologiesUsedEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for TechnologiesUsed. Leave null when extracting from resume.", "items": {"type": "number"}}, "Role": {"type": "string", "description": "The role played by the individual in the project."}, "RoleEmbedding": {"type": ["array", "null"], "description": "Server-generated embedding for <PERSON>. Leave null when extracting from resume.", "items": {"type": "number"}}}, "additionalProperties": false, "required": ["ProjectName", "ProjectNameEmbedding", "Description", "DescriptionEmbedding", "TechnologiesUsed", "TechnologiesUsedEmbedding", "Role", "RoleEmbedding"]}}}, "additionalProperties": false, "required": ["PersonalInformation", "Objective", "Education", "WorkExperience", "TotalWorkExperienceInYears", "Skills", "SkillsEmbedding", "Certifications", "Achievements", "Languages", "Projects"]}}, "required": ["Resume"], "additionalProperties": false, "description": "RESUME DATA"}}}}