/* Support.css */
.support-page {
  animation: fadeIn 0.5s ease-in;
}

.support-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 40px;
}

/* Contact Section */
.contact-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.contact-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.contact-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contact-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.contact-option:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
}

.contact-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.contact-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.contact-info p {
  font-size: 14px;
  color: var(--accent-primary);
  margin: 0 0 4px 0;
  font-weight: 500;
}

.contact-info span {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Form Section */
.form-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.form-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group .input {
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-group .input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea.input {
  resize: vertical;
  min-height: 120px;
}

/* FAQ Section */
.faq-section {
  grid-column: 1 / -1;
  background: var(--bg-primary);
  padding: 32px;
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.faq-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-item {
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
}

.faq-question {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.faq-answer {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.6;
  padding-left: 28px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .support-container {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .contact-section,
  .form-section,
  .faq-section {
    padding: 24px;
  }
  
  .contact-option {
    padding: 16px;
  }
  
  .contact-icon {
    width: 40px;
    height: 40px;
  }
  
  .faq-item {
    padding: 16px;
  }
  
  .faq-answer {
    padding-left: 24px;
  }
}
