.dashboard {
  animation: fadeIn 0.5s ease-in;
}

/* Welcome Section */
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16px;
  color: white;
}

.welcome-section .stat-item {
  color: white;
  opacity: 0.95;
}

.welcome-section .stat-icon {
  color: rgba(255, 255, 255, 0.9);
}

.welcome-content h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-content p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  opacity: 0.9;
  color: var(--text-secondary);
}

.stat-icon {
  width: 16px;
  height: 16px;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  border-left: 4px solid #3b82f6;
}

.stat-card.secondary {
  border-left: 4px solid #10b981;
}

.stat-card.accent {
  border-left: 4px solid #f59e0b;
}

.stat-card.info {
  border-left: 4px solid #8b5cf6;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header .stat-icon {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
}

.ai-icon,
.version-icon {
  font-size: 20px;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.neutral {
  color: #6b7280;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 40px;
}

.quick-actions-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 24px 0;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-content {
  flex: 1;
}

.action-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.action-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.action-arrow {
  width: 20px;
  height: 20px;
  color: #9ca3af;
  transition: all 0.2s ease;
}

.action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

/* Bottom Grid */
.bottom-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.view-all-link {
  font-size: 14px;
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
  color: var(--accent-secondary);
}

/* Activity Section */
.activity-section {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  padding: 0 8px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 10px;
  margin: 4px 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.activity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6 0%, #10b981 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover {
  background-color: var(--bg-tertiary);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 2px -4px;
  padding: 14px;
}

.activity-item:hover::before {
  opacity: 1;
}

.activity-item.clickable:hover {
  background-color: var(--bg-tertiary);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  margin: 0 -6px;
  padding: 16px;
}

.activity-item.clickable:hover::before {
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #f59e0b 100%);
}

.activity-icon-wrapper {
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.activity-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover .activity-icon-wrapper {
  transform: scale(1.08) rotate(2deg);
  background: var(--bg-primary);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.activity-item:hover .activity-icon-wrapper::before {
  opacity: 1;
}

.activity-item.clickable:hover .activity-icon-wrapper {
  transform: scale(1.1) rotate(-2deg);
  animation: dashboardIconPulse 2s ease-in-out infinite;
}

@keyframes dashboardIconPulse {
  0%, 100% {
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 5px 18px rgba(59, 130, 246, 0.2);
  }
}

.activity-icon {
  width: 16px;
  height: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.activity-item:hover .activity-icon {
  transform: scale(1.1);
}

.activity-icon.upload {
  color: #10b981;
}

.activity-icon.search {
  color: #f59e0b;
}

.activity-icon.process {
  color: #8b5cf6;
}

.activity-icon.bulk {
  color: #3b82f6;
}

.activity-icon.duplicate {
  color: #f59e0b;
}

.activity-icon.download {
  color: #06b6d4;
}

.activity-icon.delete {
  color: #ef4444;
}

.activity-icon.export {
  color: #84cc16;
}

.activity-icon.system {
  color: #6366f1;
}

.activity-icon.error {
  color: #f97316;
}

.activity-icon.default {
  color: #6b7280;
}

.activity-content {
  flex: 1;
  transition: all 0.3s ease;
}

.activity-item:hover .activity-content {
  transform: translateX(2px);
}

.activity-main {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.activity-item:hover .activity-main {
  color: var(--accent-primary);
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover .activity-meta {
  transform: translateY(-1px);
}

.activity-text {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.activity-item:hover .activity-text {
  color: var(--accent-primary);
}

.activity-action {
  font-weight: 500;
  transition: all 0.3s ease;
}

.activity-item.clickable:hover .activity-action {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.activity-detail {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.activity-item:hover .activity-detail {
  color: var(--text-primary);
}

.activity-user {
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.3s ease;
}

.activity-item:hover .activity-user {
  color: var(--accent-primary);
  font-weight: 600;
}

.activity-time {
  font-size: 12px;
  color: var(--text-tertiary);
  transition: color 0.3s ease;
}

.activity-item:hover .activity-time {
  color: var(--text-secondary);
}

.activity-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
  text-align: center;
}

.activity-empty .empty-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* Quick Stats Section */
.quick-stats-section {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.quick-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.quick-stat-item {
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.quick-stat-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.quick-stat-item.loading {
  opacity: 0.6;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 0.8; }
}

.quick-stat-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.quick-stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.quick-stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.quick-stat-change.up {
  color: var(--success);
}

.quick-stat-change.neutral {
  color: var(--text-tertiary);
}

/* Dark theme enhancements */
[data-theme="dark"] .stat-item {
  opacity: 1;
  color: var(--text-primary);
}

[data-theme="dark"] .activity-item:hover {
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.1);
}

[data-theme="dark"] .activity-item.clickable:hover {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

[data-theme="dark"] .activity-item:hover .activity-icon-wrapper {
  box-shadow: 0 3px 12px rgba(59, 130, 246, 0.2);
}

/* Focus states for accessibility */
.activity-item.clickable:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

.activity-item.clickable:focus:not(:hover) {
  transform: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bottom-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }
  
  .welcome-content h1 {
    font-size: 24px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .action-card {
    padding: 16px;
  }
  
  .activity-section,
  .quick-stats-section {
    padding: 20px;
  }

  .activity-item {
    margin: 2px 0;
    padding: 10px;
  }

  .activity-item:hover {
    transform: translateY(-1px);
    margin: 0 -2px;
    padding: 12px;
  }

  .activity-item.clickable:hover {
    transform: translateY(-1px);
    margin: 0 -3px;
    padding: 12px;
  }

  .activity-icon-wrapper {
    width: 28px;
    height: 28px;
  }

  .activity-icon {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .welcome-section {
    padding: 16px;
  }
  
  .welcome-content h1 {
    font-size: 20px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-value {
    font-size: 28px;
  }
  
  .action-card {
    padding: 12px;
  }
  
  .activity-section,
  .quick-stats-section {
    padding: 16px;
  }
}
