import React, { useState } from 'react';
import { FiHelpCircle, FiMail, FiPhone, FiMessageCircle, FiSend } from 'react-icons/fi';
import { toast } from 'react-toastify';
import './Support.css';

const Support = () => {
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'medium'
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!contactForm.subject || !contactForm.message) {
      toast.error('Please fill in all required fields');
      return;
    }
    
    toast.success('Support ticket submitted successfully!');
    setContactForm({ subject: '', message: '', priority: 'medium' });
  };

  const handleChange = (field, value) => {
    setContactForm(prev => ({ ...prev, [field]: value }));
  };

  const faqs = [
    {
      question: 'How do I upload multiple resumes at once?',
      answer: 'You can drag and drop multiple PDF files into the upload area, or click to select multiple files from your computer.'
    },
    {
      question: 'What file formats are supported?',
      answer: 'We support PDF, DOCX, TXT, JPG, and PNG formats. PDF is recommended for best results.'
    },
    {
      question: 'How does the AI search work?',
      answer: 'Our AI uses natural language processing to understand your search queries and match them with relevant resume content.'
    },
    {
      question: 'Can I export search results?',
      answer: 'Yes, you can export search results in txt format.'
    }
  ];

  return (
    <div className="support-page">
      <div className="page-header">
        <h1>Support & Help</h1>
        <p>Get help with Resume AI Agent or contact our support team</p>
      </div>

      <div className="support-container">
        {/* Contact Options */}
        <div className="contact-section">
          <h2>Contact Us</h2>
          <div className="contact-options">
            <div className="contact-option">
              <div className="contact-icon">
                <FiMail size={24} />
              </div>
              <div className="contact-info">
                <h3>Email Support</h3>
                <p><EMAIL></p>
                <span>Response within 24 hours</span>
              </div>
            </div>
            
            <div className="contact-option">
              <div className="contact-icon">
                <FiPhone size={24} />
              </div>
              <div className="contact-info">
                <h3>Phone Support</h3>
                <p>+91 98989 42935</p>
                <span>Mon-Fri, 10 AM - 7 PM IST</span>
              </div>
            </div>
            
            <div className="contact-option">
              <div className="contact-icon">
                <FiMessageCircle size={24} />
              </div>
              <div className="contact-info">
                <h3>Live Chat</h3>
                <p>Coming Soon</p>
                <span>Instant response</span>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div className="form-section">
          <h2>Send us a Message</h2>
          <form onSubmit={handleSubmit} className="contact-form">
            <div className="form-group">
              <label>Subject *</label>
              <input
                type="text"
                value={contactForm.subject}
                onChange={(e) => handleChange('subject', e.target.value)}
                placeholder="Brief description of your issue"
                className="input"
                required
              />
            </div>

            <div className="form-group">
              <label>Priority</label>
              <select
                value={contactForm.priority}
                onChange={(e) => handleChange('priority', e.target.value)}
                className="input"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div className="form-group">
              <label>Message *</label>
              <textarea
                value={contactForm.message}
                onChange={(e) => handleChange('message', e.target.value)}
                placeholder="Describe your issue or question in detail..."
                className="input"
                rows={5}
                required
              />
            </div>

            <button type="submit" className="btn btn-primary">
              <FiSend size={16} />
              Send Message
            </button>
          </form>
        </div>

        {/* FAQ Section */}
        <div className="faq-section">
          <h2>Frequently Asked Questions</h2>
          <div className="faq-list">
            {faqs.map((faq, index) => (
              <div key={index} className="faq-item">
                <div className="faq-question">
                  <FiHelpCircle size={16} />
                  <span>{faq.question}</span>
                </div>
                <div className="faq-answer">
                  {faq.answer}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Support;
