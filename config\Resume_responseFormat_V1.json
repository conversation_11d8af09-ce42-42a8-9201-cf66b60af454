{"Resume_Schema": {"type": "json_schema", "json_schema": {"name": "Resume_Schema", "strict": true, "schema": {"type": "object", "properties": {"Resume": {"type": "object", "description": "The main container for all resume details.", "properties": {"PersonalInformation": {"type": "object", "description": "Personal details of the individual.", "properties": {"FullName": {"type": "string", "description": "The full name of the individual."}, "Gender": {"type": "string", "description": "The gender of the individual."}, "BirthDate": {"type": "string", "description": "The birth date of the individual."}, "Email": {"type": "string", "description": "The email address of the individual."}, "Address": {"type": "string", "description": "The residential address of the individual."}, "ContactNumber": {"type": "string", "description": "The contact phone number."}, "LinkedInProfile": {"type": "string", "description": "URL to the individual's LinkedIn profile."}}, "additionalProperties": false, "required": ["FullName", "Gender", "BirthDate", "Email", "Address", "ContactNumber", "LinkedInProfile"]}, "Objective": {"type": "string", "description": "A career objective or summary statement."}, "Education": {"type": "array", "description": "A list of educational qualifications.", "items": {"type": "object", "description": "An educational qualification entry.", "properties": {"Degree": {"type": "string", "description": "The degree or qualification earned."}, "Institution": {"type": "string", "description": "The name of the educational institution."}, "GraduationYear": {"type": "string", "description": "The year or date of graduation."}, "GPA/Marks/%": {"type": "string", "description": "The grade, GPA, or percentage achieved."}}, "additionalProperties": false, "required": ["Degree", "Institution", "GraduationYear", "GPA/Marks/%"]}}, "WorkExperience": {"type": "array", "description": "A list of work experiences.", "items": {"type": "object", "description": "A work experience entry detailing employment information.", "properties": {"CompanyName": {"type": "string", "description": "The name of the company or organization."}, "Role": {"type": "string", "description": "The job title or role held at the company."}, "StartYear": {"type": "string", "description": "The starting year or date of the employment."}, "EndYear": {"type": "string", "description": "The ending year or date of the employment. Use 'Today' if currently employed."}, "Description/Responsibility": {"type": "string", "description": "A description of the responsibilities and tasks performed in the role."}}, "additionalProperties": false, "required": ["CompanyName", "Role", "StartYear", "EndYear", "Description/Responsibility"]}}, "TotalWorkExperienceInYears": {"type": "number", "description": "Total number of years of work experience."}, "Skills": {"type": "array", "description": "A list of professional skills.", "items": {"type": "string", "description": "A single skill."}}, "Certifications": {"type": "array", "description": "A list of professional certifications.", "items": {"type": "object", "description": "A certification detail.", "properties": {"CertificationName": {"type": "string", "description": "The name of the certification."}, "IssuingOrganization": {"type": "string", "description": "The organization that issued the certification."}, "IssueDate": {"type": "string", "description": "The date when the certification was issued."}, "ExpiryDate": {"type": "string", "description": "The expiry date of the certification, or 'None' if not applicable."}}, "additionalProperties": false, "required": ["CertificationName", "IssuingOrganization", "IssueDate", "ExpiryDate"]}}, "Achievements": {"type": "array", "description": "A list of professional achievements or awards.", "items": {"type": "object", "description": "An achievement or award entry.", "properties": {"AchievementName": {"type": "string", "description": "The name or title of the achievement."}, "IssuingOrganization": {"type": "string", "description": "The organization that recognized the achievement."}, "IssueDate": {"type": "string", "description": "The date when the achievement was awarded."}}, "additionalProperties": false, "required": ["AchievementName", "IssuingOrganization", "IssueDate"]}}, "Languages": {"type": "array", "description": "A list of languages known by the individual.", "items": {"type": "string", "description": "A single language."}}, "Projects": {"type": "array", "description": "A list of projects undertaken by the individual.", "items": {"type": "object", "description": "Details of a single project.", "properties": {"ProjectName": {"type": "string", "description": "The name of the project."}, "Description": {"type": "string", "description": "A brief description of the project."}, "TechnologiesUsed": {"type": "array", "description": "A list of technologies used in the project.", "items": {"type": "string", "description": "A technology used in the project."}}, "Role": {"type": "string", "description": "The role played by the individual in the project."}}, "additionalProperties": false, "required": ["ProjectName", "Description", "TechnologiesUsed", "Role"]}}}, "additionalProperties": false, "required": ["PersonalInformation", "Objective", "Education", "WorkExperience", "TotalWorkExperienceInYears", "Skills", "Certifications", "Achievements", "Languages", "Projects"]}}, "required": ["Resume"], "additionalProperties": false, "description": "RESUME DATA"}}}}