/* SearchResumes.css */
.search-page {
  animation: fadeIn 0.5s ease-in;
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Search Section */
.search-section {
  background: var(--bg-primary);
  padding: 32px;
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  margin-bottom: 32px;
  transition: all 0.3s ease;
}

.search-form {
  margin-bottom: 24px;
}

.search-input-group {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid var(--border-primary);
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-buttons {
  display: flex;
  gap: 12px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid var(--border-primary);
}

.filter-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.filter-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.filter-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.filter-input::placeholder {
  color: var(--text-tertiary);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.advanced-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.advanced-filter-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
}

.filter-chevron {
  transition: transform 0.2s ease;
}

.filter-chevron.rotated {
  transform: rotate(180deg);
}

.results-count {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Advanced Filter Panel */
.advanced-filter-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-sm);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.custom-date-row {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  margin-top: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.filter-select,
.filter-date-input {
  padding: 10px 12px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-select:focus,
.filter-date-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select:hover,
.filter-date-input:hover {
  border-color: var(--accent-primary);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--border-primary);
}

.filter-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* Filter Mode Toggle */
.filter-mode-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.filter-mode-toggle {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-mode-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.toggle-buttons {
  display: flex;
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 4px;
  border: 1px solid var(--border-primary);
}

.toggle-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-btn.active {
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.toggle-btn:hover:not(.active) {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Filter Context Display */
.filter-context {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-context-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.context-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.filter-mode-indicator {
  font-size: 12px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-primary);
}

.filter-context-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background: var(--accent-primary);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Results Section */
.results-section {
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-tertiary);
}

.results-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.results-actions {
  display: flex;
  gap: 12px;
}

.results-grid {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

/* Resume Cards */
.resume-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.resume-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.resume-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-primary);
}

.resume-card:hover::before {
  opacity: 1;
}

.resume-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
}

.favorite-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.favorite-btn:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: scale(1.1);
}

.favorite-btn.favorited {
  color: var(--accent-primary);
  background: var(--bg-tertiary);
}

.favorite-btn.favorited:hover {
  color: #dc2626;
  transform: scale(1.1);
}

.resume-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.resume-info {
  flex: 1;
}

.resume-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.resume-title {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
}

.resume-score {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.score-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--success);
  background: var(--bg-tertiary);
  padding: 2px 8px;
  border-radius: 12px;
}

.resume-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
}

.detail-item.clickable {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  margin: -4px -8px;
  position: relative;
}

.detail-item.clickable:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.detail-item.email-item.clickable:hover {
  background: var(--bg-tertiary);
  color: var(--success);
}

.detail-item.phone-item.clickable:hover {
  background: var(--bg-tertiary);
  color: var(--error);
}

.external-icon {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: auto;
}

.detail-item.clickable:hover .external-icon {
  opacity: 1;
}

.resume-content {
  margin-bottom: 20px;
}

.content-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.content-section {
  margin-bottom: 16px;
}

.content-row .content-section {
  margin-bottom: 0;
}

.content-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.content-section p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.skill-tag {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--border-primary);
}

/* Added Date Styling for Search Results */
.resume-card .added-date-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: var(--text-tertiary);
  font-size: 12px;
  font-weight: 500;
  background: var(--bg-tertiary);
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

.resume-card .added-date {
  margin: 0;
}

.summary-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resume-actions {
  display: flex;
  gap: 12px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 64px 32px;
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: var(--text-tertiary);
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.empty-state p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.search-examples {
  background: var(--bg-tertiary);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.search-examples h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.search-examples ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-examples li {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.search-examples li:before {
  content: "•";
  color: var(--accent-primary);
  position: absolute;
  left: 0;
}

/* No Results State */
.no-results-state {
  text-align: center;
  padding: 64px 32px;
  background: var(--bg-primary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.no-results-state .empty-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: var(--text-tertiary);
}

.no-results-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.no-results-state p {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.search-suggestions {
  background: var(--bg-tertiary);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.search-suggestions h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.search-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-suggestions li {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
}

.search-suggestions li:before {
  content: "•";
  color: var(--warning);
  position: absolute;
  left: 0;
}

/* Search History Styles */
.history-dropdown {
  position: relative;
  display: inline-block;
}

.history-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.history-dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 400px;
  max-width: 500px;
  max-height: 500px;
  overflow: hidden;
  margin-top: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.history-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.history-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-empty {
  padding: 32px 20px;
  text-align: center;
  color: var(--text-tertiary);
}

.history-empty p {
  margin: 0;
  font-size: 14px;
}

.history-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-primary);
  transition: background-color 0.2s ease;
  position: relative;
}

.history-item:hover {
  background: var(--bg-tertiary);
}

.history-item:last-child {
  border-bottom: none;
}

.history-item-content {
  flex: 1;
  cursor: pointer;
  min-width: 0;
}

.history-query {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
  word-break: break-word;
}

.history-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.history-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-tertiary);
}

.history-results {
  font-size: 12px;
  color: var(--accent-primary);
  font-weight: 500;
}

.history-gpt-response {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  margin-top: 4px;
  padding: 6px 8px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  border-left: 3px solid var(--accent-primary);
}

.history-delete-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-left: 8px;
  flex-shrink: 0;
}

.history-delete-btn:hover {
  color: var(--danger);
  background: var(--bg-tertiary);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 6px;
}

.btn-danger {
  background: var(--danger);
  color: white;
  border: 1px solid var(--danger);
}

.btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* Responsive adjustments for history */
@media (max-width: 768px) {
  .history-dropdown-content {
    min-width: 300px;
    max-width: 90vw;
    right: -20px;
  }

  .history-btn {
    font-size: 14px;
    padding: 8px 12px;
  }

  .search-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-buttons {
    width: 100%;
  }

  .search-buttons .btn {
    flex: 1;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .custom-date-row {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    justify-content: center;
  }

  .filter-mode-toggle {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toggle-buttons {
    justify-content: center;
  }

  .filter-context-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filter-context-tags {
    justify-content: center;
  }

  .results-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .results-header {
    padding: 20px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .resume-actions {
    flex-direction: column;
  }

  .content-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .content-row .content-section {
    margin-bottom: 12px;
  }
}

/* Pagination Styles */
.pagination-section {
  margin-top: 32px;
  padding: 24px 0;
  border-top: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.pagination-btn {
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .pagination-btn {
    min-width: 36px;
    height: 36px;
    font-size: 13px;
  }
}
