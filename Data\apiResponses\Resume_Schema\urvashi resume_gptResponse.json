{"id": "chatcmpl-B4kDxaTGzoE8Co2iWyFkfev2Gyw0R", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"URVASHI PALIWAL\",\"Gender\":\"Female\",\"BirthDate\":19890213,\"Email\":\"<EMAIL>\",\"Address\":\"13-B KAMLA NEHRU COLONY, KILA MAIDAN, INDORE, PIN CODE-452015, MP\",\"ContactNumber\":\"+918770129092\",\"LinkedInProfile\":\"\"},\"Objective\":\"\",\"Education\":[{\"Degree\":\"B.Ed.\",\"Institution\":\"Akshay Academy College, Indore\",\"GraduationYear\":\"\",\"GPA/Marks/%\":90},{\"Degree\":\"B.Com.\",\"Institution\":\"Shri C.M.K. Van<PERSON>jya Mahavidyalaya, Indore\",\"GraduationYear\":\"\",\"GPA/Marks/%\":70},{\"Degree\":\"Higher secondary\",\"Institution\":\"Shri Cloth Market VBM Girls H S School, Indore\",\"GraduationYear\":\"2006\",\"GPA/Marks/%\":75},{\"Degree\":\"High school\",\"Institution\":\"Shri Cloth Market VBM Girls HS School, Indore\",\"GraduationYear\":\"2004\",\"GPA/Marks/%\":72}],\"WorkExperience\":[{\"CompanyName\":\"VISHIHT SCHOOL OF MANAGEMENT, Indore\",\"Role\":\"Telecaller\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Three months' experience of telecalling.\"},{\"CompanyName\":\"ICICI BANK\",\"Role\":\"Cashier\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Three-months experience as per cashier post.\"},{\"CompanyName\":\"Kautilya Academy Indore\",\"Role\":\"Employee\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"One-year experience.\"},{\"CompanyName\":\"Indore International School\",\"Role\":\"Teacher (TGT)\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"One Year Teaching Experience.\"}],\"TotalWorkExperienceInYears\":0,\"Skills\":[\"Basic computer knowledge\",\"Good Knowledge of Internet\",\"Good knowledge of Power-Point and MS- world, MS-Excel, Tally\"],\"Certifications\":[],\"Achievements\":[{\"AchievementName\":\"CTET Qualified II Level Qualified\",\"IssuingOrganization\":\"\",\"IssueDate\":0}],\"Languages\":[\"English\",\"Hindi\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": **********, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_f9f4fb6dbf", "usage": {"completion_tokens": 459, "prompt_tokens": 3013, "total_tokens": 3472, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 2944}}}