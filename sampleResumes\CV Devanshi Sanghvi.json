{"Resume": {"PersonalInformation": {"FullName": "DEVANSHI DIPAK SANG", "Gender": "", "BirthDate": "", "Email": "devanshisanghvi5390@gmailcom", "Address": "Park Colony, Jamnagar-361008,Gujarat", "ContactNumber": "**********", "LinkedInProfile": "/www.linkedin.com/in/devanshi-sanghvi"}, "Objective": "I am a passionate and focused professional looking for a job opportunity in the field of Audit, Taxation, Finance in corporate or consulting firm while aiming towards growth and better prospects\nfor the organization through my competency, hard work, dedication and adaptability at\nmultidisciplinary jobs", "Education": [{"Degree": "CPT(June 2016)", "Institution": "The Institute of Chartered\nAccountants of India", "GraduationYear": "June 201-present", "GPA/Marks/%": "125/200 Marks"}, {"Degree": "B.com", "Institution": "Saurashtra University\n(M<PERSON>P. Shah Commerce\nCollege- Jamnagar", "GraduationYear": "November 2016-\nAugust 2019", "GPA/Marks/%": "64.409%"}, {"Degree": "12th", "Institution": "GSHSEB\n(<PERSON><PERSON><PERSON><PERSON><PERSON> Sai Vidhyalaya- Jamnagar)", "GraduationYear": "March 2016", "GPA/Marks/%": "85%"}], "WorkExperience": [{"CompanyName": "<PERSON><PERSON><PERSON> &\nAssociates- Jamnaga", "Role": "Tax Return\nPreparer", "StartYear": "Tax Return\nSeason\n2022", "EndYear": "", "Description/Responsibility": "Dealt with issues relating to\nreturn uploading, processing &\nrefund re-issue of Income Tax\nReturns of individuals & firms\nnot liable to audit  Arrears of Salary Returns"}], "Skills": ["Knowledge of Microsoft Word, Excel, Tally ERP 9 & 7, Spectrum.  Team Management & Meeting Deadlines.  Interpersonal Communication & Client Interaction (Query solving in person, telephone & e-mail)  Public Speaking (Participated in debate, extempore & seminars organized by ICAI)  Languages Known: English, Hindi, and Gujarat"], "Certifications": [{"CertificationName": " Certificate of Excellence", "IssuingOrganization": "INDmoney", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [""], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}