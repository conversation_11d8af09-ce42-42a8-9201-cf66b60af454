"""
Example usage of the integrated MongoDB + ChromaDB workflow.

This script demonstrates how to use the new integrated system where:
1. Documents are processed and stored in MongoDB
2. MongoDB IDs are automatically added to structured_data
3. Embeddings are stored in ChromaDB with MongoDB IDs in metadata
"""

import asyncio
import os
from mongoDBInsertion import DocumentProcessor, process_documents_from_folder
from chromdb_processor import ChromaDBProcessor
from bson import ObjectId

async def example_process_folder():
    """Example: Process a folder of documents with MongoDB + ChromaDB integration."""
    
    print("📁 Processing documents with MongoDB + ChromaDB integration")
    print("=" * 60)
    
    folder_path = "./Data/inputData/Resume_Schema"  # Adjust path as needed
    
    # Process all documents in the folder
    results = await process_documents_from_folder(
        folder_path=folder_path,
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        n_workers=4,
        enable_chromadb=True,  # Enable ChromaDB integration
        chroma_host="localhost",
        chroma_port=8000
    )
    
    print(f"✅ Processing completed!")
    print(f"   Successfully processed: {results.get('successful', 0)}")
    print(f"   Skipped (duplicates): {results.get('skipped', 0)}")
    print(f"   Failed: {results.get('failed', 0)}")
    
    return results

async def example_process_single_file():
    """Example: Process a single file with MongoDB + ChromaDB integration."""
    
    print("\n📄 Processing single file with MongoDB + ChromaDB integration")
    print("=" * 60)
    
    # Initialize processor with ChromaDB enabled
    processor = DocumentProcessor(
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        enable_chromadb=True,
        chroma_host="localhost",
        chroma_port=8000
    )
    
    # Process a single file
    file_path = "./Data/inputData/Resume_Schema/sample_resume.pdf"  # Adjust path as needed
    
    if os.path.exists(file_path):
        temp_dir = "./temp"
        os.makedirs(temp_dir, exist_ok=True)
        
        success = await processor.process_single_file(file_path, temp_dir)
        
        if success:
            print(f"✅ Successfully processed: {file_path}")
        else:
            print(f"❌ Failed to process: {file_path}")
    else:
        print(f"⚠️  File not found: {file_path}")

def example_query_chromadb():
    """Example: Query ChromaDB to find documents by MongoDB ID."""
    
    print("\n🔍 Querying ChromaDB with MongoDB ID")
    print("=" * 60)
    
    # Initialize ChromaDB processor
    chromadb_processor = ChromaDBProcessor()
    
    # Example: Find all embeddings for a specific MongoDB ID
    mongodb_id = "507f1f77bcf86cd799439011"  # Replace with actual MongoDB ID
    
    try:
        results = chromadb_processor.collection.get(
            where={"mongodb_id": mongodb_id},
            include=["metadatas", "documents"]
        )
        
        if results and results['ids']:
            print(f"✅ Found {len(results['ids'])} embeddings for MongoDB ID: {mongodb_id}")
            
            for i, (doc_id, metadata, document) in enumerate(zip(
                results['ids'], 
                results['metadatas'], 
                results['documents']
            )):
                print(f"\n📄 Embedding {i+1}:")
                print(f"   ChromaDB ID: {doc_id}")
                print(f"   Embedding Type: {metadata.get('embedding_type', 'N/A')}")
                print(f"   Full Name: {metadata.get('FullName', 'N/A')}")
                print(f"   Document Preview: {document[:100]}...")
        else:
            print(f"❌ No embeddings found for MongoDB ID: {mongodb_id}")
            
    except Exception as e:
        print(f"❌ Error querying ChromaDB: {e}")

def example_search_by_name():
    """Example: Search ChromaDB by person name."""
    
    print("\n🔎 Searching ChromaDB by person name")
    print("=" * 60)
    
    chromadb_processor = ChromaDBProcessor()
    
    search_name = "John Doe"  # Replace with actual name to search
    
    try:
        results = chromadb_processor.collection.get(
            where={"FullName": {"$contains": search_name}},
            include=["metadatas"]
        )
        
        if results and results['ids']:
            print(f"✅ Found {len(results['ids'])} embeddings for name containing: {search_name}")
            
            # Group by MongoDB ID to show unique resumes
            mongodb_ids = set()
            for metadata in results['metadatas']:
                mongodb_ids.add(metadata.get('mongodb_id'))
            
            print(f"📊 Found {len(mongodb_ids)} unique resumes")
            
            for mongodb_id in list(mongodb_ids)[:3]:  # Show first 3
                print(f"   MongoDB ID: {mongodb_id}")
                
        else:
            print(f"❌ No embeddings found for name: {search_name}")
            
    except Exception as e:
        print(f"❌ Error searching ChromaDB: {e}")

def example_delete_document():
    """Example: Delete a document from both MongoDB and ChromaDB."""
    
    print("\n🗑️  Deleting document from MongoDB and ChromaDB")
    print("=" * 60)
    
    # Initialize processor
    processor = DocumentProcessor(
        enable_chromadb=True,
        chroma_host="localhost",
        chroma_port=8000
    )
    
    # Example MongoDB ID to delete (replace with actual ID)
    mongodb_id_str = "507f1f77bcf86cd799439011"
    
    try:
        mongodb_id = ObjectId(mongodb_id_str)
        success = processor.delete_document_by_id(mongodb_id)
        
        if success:
            print(f"✅ Successfully deleted document: {mongodb_id}")
        else:
            print(f"❌ Failed to delete document: {mongodb_id}")
            
    except Exception as e:
        print(f"❌ Error deleting document: {e}")

async def main():
    """Main example function."""
    
    print("🚀 MongoDB + ChromaDB Integration Examples")
    print("=" * 80)
    
    # Example 1: Process folder
    await example_process_folder()
    
    # Example 2: Process single file
    await example_process_single_file()
    
    # Example 3: Query ChromaDB
    example_query_chromadb()
    
    # Example 4: Search by name
    example_search_by_name()
    
    # Example 5: Delete document (commented out for safety)
    # example_delete_document()
    
    print("\n🏁 Examples completed!")
    print("\nKey Benefits of the Integration:")
    print("✅ MongoDB IDs are automatically included in ChromaDB metadata")
    print("✅ Structured data flows seamlessly from MongoDB to ChromaDB")
    print("✅ No need to read JSON files - direct data processing")
    print("✅ Consistent data between MongoDB and ChromaDB")
    print("✅ Easy cleanup - delete from both systems with one call")

if __name__ == "__main__":
    asyncio.run(main())
