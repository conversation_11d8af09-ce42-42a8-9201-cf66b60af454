{"version": "0.2.0", "configurations": [{"name": "HGC - Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app:app", "--reload", "--port", "8000"], "env": {"PYTHONPATH": "${workspaceFolder}"}, "python": "${workspaceFolder}\\envResumeAgent\\Scripts\\python.exe", "cwd": "${workspaceFolder}", "console": "integratedTerminal", "justMyCode": true}, {"name": "HGC - Streamlit: Run frontendV3.py", "type": "python", "request": "launch", "module": "streamlit", "args": ["run", "frontendV3.py"], "python": "${workspaceFolder}/envResumeAgent/Scripts/python.exe", "preLaunchTask": "activateEnv", "console": "integratedTerminal", "justMyCode": true}]}