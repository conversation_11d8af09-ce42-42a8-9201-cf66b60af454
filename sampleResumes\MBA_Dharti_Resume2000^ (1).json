{"Resume": {"PersonalInformation": {"FullName": "<PERSON> ", "Gender": "", "BirthDate": "23/04/2000", "Email": "<EMAIL>", "Address": "C-408 Panchshlok Recidence\n <PERSON>.", "ContactNumber": "+91- 9054920243", "LinkedInProfile": ""}, "Objective": "To succeed in an environment of growth and excellence and get a job which provides me job \n satisfaction and self- development and help me to achieve personal as well as organization goals. ", "Education": [{"Degree": "MBA(Finance)", "Institution": "Silver Oak University ", "GraduationYear": "2023", "GPA/Marks/%": "CGPA 8.80"}, {"Degree": "B.Com", "Institution": "Lokmanya Commerce of \n College ", "GraduationYear": "2021", "GPA/Marks/%": "CGPA 5.67"}, {"Degree": "HSC (10+2)", "Institution": "Mani-prabhu Higher /nSecondary School ", "GraduationYear": "2018", "GPA/Marks/%": "60.69%"}, {"Degree": "SSC", "Institution": "Gyanda Girls Higher Secondary /nSchool ", "GraduationYear": "2016", "GPA/Marks/%": "63.80%"}], "WorkExperience": [{"CompanyName": "Confiance BizSol Private Limited", "Role": " Accounts Trainee US Finance & Accounts", "StartYear": "20/02/2023", "EndYear": "10/11/2023", "Description/Responsibility": "Process journal entries and correct records to ensure accuracy.\n Reconciled and processed credit card bills.\nPrioritized invoices payments according to payments due dates.\nEstablished monitored and maintained standard costs on a regular basis.\n Maintained files and documentation thoroughly and accurately, in accordance with company policy\n and accepted accounting check requests.\nCompleted daily cash function like account tracking, payroll and wage allocation, budgeting all types\n of cash and banking reconciliation.\nRecorded transaction in QuickBooks, and reply mailed invoices to client."}], "Skills": ["Advance Diploma In Computer Application With ‘A Grade’.\n TallyERP9. \nCourse On Computer Concept (CCC). \n Microsoft Office (Word, Excel, PowerPoint). \nOperating System: Windows 7, Windows 10.\nQuickBooks and Houzz pro.\nGL Accounting"], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [" English, Gujrati, Hindi. "], "Projects": [{"ProjectName": "", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}