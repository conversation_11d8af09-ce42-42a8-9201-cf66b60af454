{"id": "chatcmpl-B4kDxo3tFhsLQNIKgiyKLfDEW8GXb", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"SMITA MEDHE\",\"Gender\":\"Female\",\"BirthDate\":********,\"Email\":\"<EMAIL>\",\"Address\":\"Res. 303 Manawata Nagar, Kanadiya Road, Indore(M.P.)\",\"ContactNumber\":\"**********\",\"LinkedInProfile\":\"\"},\"Objective\":\"\",\"Education\":[{\"Degree\":\"BA\",\"Institution\":\"DAVV Indore\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Sanskrit Purv Madhima\",\"Institution\":\"Mumbai\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"Vedh <PERSON>\",\"Role\":\"Accountant\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"<PERSON><PERSON> Guru<PERSON>l\",\"Role\":\"Teacher\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Adarsh <PERSON>shu Vihar\",\"Role\":\"Teacher & Office Work\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"RGCSM\",\"Role\":\"Teacher & Office Work\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Spring Valley School\",\"Role\":\"Receptionist\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Vedaant Vidhykulum School\",\"Role\":\"Hostel Warden\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Vedaant Vidhyakulum School\",\"Role\":\"Back Office\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Present\"}],\"TotalWorkExperienceInYears\":16,\"Skills\":[],\"Certifications\":[{\"CertificationName\":\"Diploma DCA\",\"IssuingOrganization\":\"\",\"IssueDate\":0,\"ExpiryDate\":0},{\"CertificationName\":\"COPA & OACOS ITI Government\",\"IssuingOrganization\":\"\",\"IssueDate\":0,\"ExpiryDate\":0}],\"Achievements\":[],\"Languages\":[\"Hindi\",\"English\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": 1740470697, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_f9f4fb6dbf", "usage": {"completion_tokens": 452, "prompt_tokens": 2617, "total_tokens": 3069, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 2560}}}