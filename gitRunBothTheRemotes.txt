git remote add origin http://192.168.1.18:3000/riveredge_admin/sql-agent-task-submission.git
git remote add github https://github.com/drivetest721/resumeAI.git




Create an alias (just once):
git config alias.pushall '!git push origin && git push github'
Then just run:
git pushall



git add . or git add <filename>

git pushall

git commit -m "Your commit message here"



# ----------------------------------------------

git add .
git commit -m "Bug Fix: Toggle button hidden in minimized view"
git pushall
