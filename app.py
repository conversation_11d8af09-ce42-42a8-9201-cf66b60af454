import json
import logging,os
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
import mysql.connector
from openai import OpenAI
from config import DB_CONFIG
from helpers import ReadTxtFile, LoadJsonFile
from helperMongoDb import MongoDBClient
import csv
import io
from fastapi.encoders import jsonable_encoder
from bson import ObjectId
import hashlib
import shutil
from pathlib import Path
from typing import List
from datetime import datetime
from extractDataParallel import CProcessDocument
import asyncio

# Added Middleware to access api from sever
from fastapi.middleware.cors import CORSMiddleware


# Define log directory and file path
log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")

# Create logs directory if it doesn't exist
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    filename=log_file,  # Log file name
    level=logging.INFO,  # Log level
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",  # Log format
    filemode="a",  # Append mode (use "w" for overwrite mode)
)
logger = logging.getLogger(__name__)

# Initialize FastAPI and OpenAI client
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Or use ["*"] to allow all origins (not recommended for production)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

openAiClient = OpenAI()

# Create an instance of MongoDBClient with error handling
try:
    mongo_client = MongoDBClient()
    MONGODB_AVAILABLE = True
    print("✅ MongoDB connection successful")
except Exception as e:
    print(f"⚠️ MongoDB connection failed: {e}")
    print("📁 Will use sample data as fallback")
    mongo_client = None
    MONGODB_AVAILABLE = False

# Upload configuration
UPLOAD_FOLDER = "./Data/inputData/Resume_Schema"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def calculate_checksum(file_data: bytes) -> str:
    """Calculate SHA-256 checksum from bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_data)
    return sha256_hash.hexdigest()

def log_activity(action: str, detail: str, user: str = "Admin"):
    """Log an activity (placeholder for future activity logging)"""
    logger.info(f"Activity: {action} - {detail} by {user}")
    print(f"📝 Activity: {action} - {detail} by {user}")

import json

def read_json_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        return data

def load_sample_resumes():
    """Load sample resume data when MongoDB is not available"""
    try:
        FILE_PATH = "resource/SampleResume.json"
        sample_data = read_json_file(FILE_PATH)

        # Create multiple sample resumes based on the single sample
        sample_resumes = []
        for i in range(3):
            resume_copy = json.loads(json.dumps(sample_data))  # Deep copy
            resume_copy['_id'] = f"sample_{i+1}"
            if i == 1:
                resume_copy['Resume']['PersonalInformation']['FullName'] = "Jane Smith"
                resume_copy['Resume']['PersonalInformation']['Email'] = "<EMAIL>"
                resume_copy['Resume']['Skills'] = ["React", "JavaScript", "CSS", "HTML"]
            elif i == 2:
                resume_copy['Resume']['PersonalInformation']['FullName'] = "Mike Johnson"
                resume_copy['Resume']['PersonalInformation']['Email'] = "<EMAIL>"
                resume_copy['Resume']['Skills'] = ["Docker", "Kubernetes", "AWS", "DevOps"]
            sample_resumes.append(resume_copy)

        return sample_resumes
    except Exception as e:
        print(f"Error loading sample data: {e}")
        return []

def search_sample_data(naturalQuery: str):
    """Search through sample data when MongoDB is not available"""
    sample_resumes = load_sample_resumes()

    # Simple keyword matching for demo purposes
    query_lower = naturalQuery.lower()
    filtered_resumes = []

    for resume in sample_resumes:
        resume_text = json.dumps(resume).lower()

        # Check for common search terms
        if any(word in resume_text for word in ['python', 'developer', 'engineer', 'software']) and 'python' in query_lower:
            filtered_resumes.append(resume)
        elif any(word in resume_text for word in ['react', 'javascript', 'frontend']) and any(term in query_lower for term in ['react', 'javascript', 'frontend']):
            filtered_resumes.append(resume)
        elif any(word in resume_text for word in ['docker', 'kubernetes', 'devops']) and any(term in query_lower for term in ['docker', 'devops', 'kubernetes']):
            filtered_resumes.append(resume)

    # If no specific matches, return all for broad queries
    if not filtered_resumes and any(word in query_lower for word in ['all', 'show', 'list', 'get', 'resumes', 'candidates']):
        filtered_resumes = sample_resumes
        result_text = f"Showing all {len(filtered_resumes)} resumes from sample data"
    else:
        result_text = f"Found {len(filtered_resumes)} matching resumes from sample data based on your query: '{naturalQuery}'"

    return {
        "query": [{"$match": {"$text": {"$search": naturalQuery}}}],
        "listOfDict": filtered_resumes,
        "result": result_text
    }


def processQuery(naturalQuery: str) -> str:
    """
    Converts a natural language query into an SQL query using OpenAI.

    Args:
        naturalQuery (str): The natural language query provided by the user.

    Returns:
        str: The generated SQL query.

    Raises:
        HTTPException: If there is an error in generating or parsing the AI response.
    """
    logger.info("Processing natural language query: %s", naturalQuery)
    strSystemPromptInputPart1 = ReadTxtFile("resource/strSystemPromptInput.txt")
    # strDbAndTables = ReadTxtFile("resource/databaseCreateTables.sql")
    FILE_PATH = "resource\\SampleResume.json"
    resume_data = read_json_file(FILE_PATH)
    strSystemPromptInput = strSystemPromptInputPart1 + str(resume_data)

    responseFormatInput = LoadJsonFile("resource/strResponseFormatInput.json")

    try:
        logger.info("Sending query to OpenAI for processing.")
        completion = openAiClient.chat.completions.create(
            model="gpt-4.1-2025-04-14",
            messages=[
                {"role": "system", "content": strSystemPromptInput},
                {"role": "user", "content": naturalQuery}
            ],
            temperature=0.7,
            response_format=responseFormatInput
        )
        logger.info("Received response from OpenAI.")
    except Exception as e:
        logger.error("Error generating SQL query: %s", e)
        raise HTTPException(status_code=500, detail="Error generating SQL query.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        # MongoDbQuery = responseContent["SqlQuery"]
        # logger.info("Generated SQL query: %s", MongoDbQuery)
        # return MongoDbQuery

        strMongoDbQuery = responseContent["MongoDbQuery"]
        logger.info("Generated SQL query: %s", strMongoDbQuery)
        return strMongoDbQuery

    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing OpenAI response: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI response.")

def getOutputQueryText(naturalQuery: str, MongoDbQuery: str, listSqlOutput: list) -> str:
    """
    Converts SQL output into a human-readable explanation using AI.

    Args:
        naturalQuery (str): The original natural language query.
        MongoDbQuery (str): The generated SQL query.
        listSqlOutput (list): The output results from the SQL query.

    Returns:
        str: A human-readable explanation of the SQL query result.

    Raises:
        HTTPException: If there is an error in processing the AI output response.
    """
    logger.info("Generating textual explanation for SQL output.")
    strSystemPromptOutput = ReadTxtFile("resource/strSystemPromptOutput.txt")
    strUserPromptOutput = (
        f"Natural Question: {naturalQuery},\n"
        f"SQL Query: {MongoDbQuery},\n"
        f"SQL Output: {listSqlOutput}"
    )
    if len(strUserPromptOutput)>20000:
        strUserPromptOutput = strUserPromptOutput[:20000]
    responseFormatOutput = LoadJsonFile("resource/strResponseFormatOutput.json")

    try:
        completion = openAiClient.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": strSystemPromptOutput},
                {"role": "user", "content": strUserPromptOutput}
            ],
            temperature=0.7,
            response_format=responseFormatOutput
        )
        logger.info("Received textual explanation from OpenAI.")
    except Exception as e:
        logger.error("Error generating output query text: %s", e)
        raise HTTPException(status_code=500, detail="Error generating output text.")

    try:
        responseContent = json.loads(completion.choices[0].message.content)
        strOutputText = responseContent["SqlQueryOutputText"]
        return strOutputText
    except (KeyError, json.JSONDecodeError) as e:
        logger.error("Error parsing output response from OpenAI: %s", e)
        raise HTTPException(status_code=500, detail="Error processing AI output response.")


import json
import ast

def load_query_string(query_string):
    try:
        # First try to parse as JSON (works for double quotes)
        result = json.loads(query_string)
    except json.JSONDecodeError:
        try:
            # If JSON fails, try ast.literal_eval (works for single quotes)
            result = ast.literal_eval(query_string)
        except (ValueError, SyntaxError) as e:
            raise ValueError(f"Invalid query string format: {e}")
    return result




import pandas as pd
import mysql.connector
from fastapi import HTTPException



@app.post("/upload")
async def upload_resumes(files: List[UploadFile] = File(...)):
    """
    Upload and process resume files - mirrors frontendV3.py functionality

    Args:
        files: List of uploaded PDF files

    Returns:
        dict: Upload results with processing status
    """
    try:
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")

        processed_files = []
        duplicate_files = []
        saved_files = []

        # Process each uploaded file
        for uploaded_file in files:
            if not uploaded_file.filename:
                continue

            # Validate file type
            if not uploaded_file.filename.lower().endswith('.pdf'):
                raise HTTPException(status_code=400, detail=f"File {uploaded_file.filename} is not a PDF")

            try:
                # Read file data
                file_data = await uploaded_file.read()
                checksum = calculate_checksum(file_data)

                # Check for duplicates using MongoDB
                if MONGODB_AVAILABLE and mongo_client:
                    if mongo_client.is_duplicate(checksum):
                        duplicate_files.append(uploaded_file.filename)
                        continue

                # Save file to upload directory
                file_path = Path(UPLOAD_FOLDER) / uploaded_file.filename
                with open(file_path, "wb") as f:
                    f.write(file_data)

                saved_files.append(str(file_path))

                # Log activity
                log_activity('Resume uploaded', uploaded_file.filename)

            except Exception as e:
                logger.error(f"Error processing file {uploaded_file.filename}: {e}")
                raise HTTPException(status_code=500, detail=f"Error processing {uploaded_file.filename}: {str(e)}")

        # Process saved files using the same pipeline as frontendV3.py
        if saved_files:
            try:
                # Initialize the document processor
                processor = CProcessDocument(strVendorName="Resume_Schema")

                # Process the uploaded documents
                processor.MprocessAllDocuments(iFilesToProcess=len(saved_files))

                log_activity('Documents processed', f'{len(saved_files)} files processed successfully')

            except Exception as e:
                logger.error(f"Error during document processing: {e}")
                # Don't fail the upload if processing fails, just log it
                log_activity('Processing error', f'Error processing documents: {str(e)}')

        return JSONResponse(content={
            "message": f"Successfully processed {len(saved_files)} files",
            "processed_files": len(saved_files),
            "duplicate_files": len(duplicate_files),
            "duplicates": duplicate_files,
            "saved_files": [Path(f).name for f in saved_files],
            "mongodb_available": MONGODB_AVAILABLE
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload endpoint error: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.get("/")
def root():
    """
    Root endpoint to check if the API is running.

    Returns:
        dict: A simple message indicating the API status.
    """
    return {"message": "SQL Agent API is running", "mongodb_available": MONGODB_AVAILABLE}

@app.get("/test")
def test_endpoint():
    """
    Test endpoint to check sample data loading.
    """
    try:
        sample_resumes = load_sample_resumes()
        return {"status": "success", "count": len(sample_resumes), "mongodb_available": MONGODB_AVAILABLE}
    except Exception as e:
        return {"status": "error", "error": str(e)}

@app.get("/showall")
def show_all():
    """
    Endpoint to get all resumes from MongoDB or fallback to sample data.

    Returns:
        list: List of all resume documents.
    """
    from helperMongoDb import DATABASE_NAME, COLLECTION_NAME
    print(f"🔍 DEBUG: Using database: {DATABASE_NAME}, collection: {COLLECTION_NAME}")
    print(f"🔍 DEBUG: MONGODB_AVAILABLE: {MONGODB_AVAILABLE}")
    print(f"🔍 DEBUG: mongo_client: {mongo_client}")

    if MONGODB_AVAILABLE and mongo_client:
        try:
            print(f"🔍 DEBUG: mongo_client.db.name: {mongo_client.db.name}")
            cursorShowAll = mongo_client.get_all_resume_data()
            lsShowAll = [doc for doc in cursorShowAll]

            if not lsShowAll:
                print("📁 No resumes in MongoDB, using sample data")
                return load_sample_resumes()

            print(f"📊 Loaded {len(lsShowAll)} resumes from MongoDB database: {mongo_client.db.name}")
            return jsonable_encoder(lsShowAll, custom_encoder={ObjectId: str})
        except Exception as e:
            print(f"⚠️ MongoDB error: {e}, using sample data")
            return load_sample_resumes()
    else:
        print("📁 Using sample data (MongoDB not available)")
        return load_sample_resumes()


@app.get("/query/")
def queryNlp(naturalQuery: str):
    """
    API endpoint to process a natural language query, execute the corresponding SQL,
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    if MONGODB_AVAILABLE and mongo_client:
        try:
            logger.info("Processing user query: %s", naturalQuery)
            strPipeline = processQuery(naturalQuery)
            listPipeline = load_query_string(strPipeline)
            cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
            result = [doc for doc in cursorMongoDb]
            strOutputText = getOutputQueryText(naturalQuery, listPipeline, result)
            return {"query": listPipeline, "listOfDict":jsonable_encoder(result, custom_encoder={ObjectId: str}), "result": strOutputText}

        except Exception as e:
            logger.error("Error processing query: %s", e)
            # Fallback to sample data search
            return search_sample_data(naturalQuery)
    else:
        # Use sample data when MongoDB is not available
        return search_sample_data(naturalQuery)


@app.get("/query/all")
def queryNlpAll():
    """
    API endpoint to process a natural language query, execute the corresponding SQL,
    and return the result with a human-readable explanation.

    Args:
        naturalQuery (str): The user's natural language query.

    Returns:
        dict: A dictionary containing the SQL query and the human-readable result.

    Raises:
        HTTPException: If any step in query processing fails.
    """
    try:
        # logger.info("Processing user query: %s", naturalQuery)
        listPipeline = [{"$match": {"Resume": {"$exists": True}}}]
        cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
        result = [doc for doc in cursorMongoDb]
        strOutputText = "Showing all resumes, each with various educational backgrounds and professional experiences in teaching subjects such as Mathematics, English, Physics, Science, History, Social Studies, Environmental Science, Computer Science, Commerce, and Geography."
        return {"query": listPipeline, "listOfDict":result, "result": strOutputText}

    except Exception as e:
        logger.error("Error processing query: %s", e)
        raise HTTPException(status_code=400, detail=str(e))



if __name__ == "__main__":
    import uvicorn
    from helperMongoDb import DATABASE_NAME, COLLECTION_NAME

    print("🚀 Starting Resume AI Agent API Server...")
    print(f"📊 MongoDB connected to: {DATABASE_NAME}.{COLLECTION_NAME}")
    print("🔗 API will be available at: http://************:8003")
    print("📋 Health Check: http://************:8003/docs")

    # Start the FastAPI server
    uvicorn.run(app, host="0.0.0.0", port=8003)