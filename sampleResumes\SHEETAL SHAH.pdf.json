{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON>", "Gender": "Female", "BirthDate": "27th Aug 1991 ", "Email": "<EMAIL>", "Address": "30 Shubh Tenament I O C Road Chandhkheda Ahmadabad 382424.", "ContactNumber": "7041896192", "LinkedInProfile": ""}, "Objective": "To work in an organization makes me learn and develop as a valuable asset of an organization;\n to work in such a culture where I am given the chance to utilize my skills and knowledge\n enriches.", "Education": [{"Degree": "MBA", "Institution": "N.R. Institute of Business \nManagement (GLS-MBA) ", "GraduationYear": "2014", "GPA/Marks/%": "7.29 "}, {"Degree": "B. Com", "Institution": "Gujarat University ", "GraduationYear": "2011", "GPA/Marks/%": "54.00"}, {"Degree": "HSC", "Institution": "GHSEB", "GraduationYear": "2008", "GPA/Marks/%": "70.83 "}, {"Degree": "SSC", "Institution": "GSEB", "GraduationYear": "2006", "GPA/Marks/%": "73.83 "}], "WorkExperience": [{"CompanyName": "Relay Human Cloud India Pvt limited", "Role": "Accounting Executive", "StartYear": "Sep \n2022", "EndYear": "'Present'", "Description/Responsibility": "1. Bank Entries and Bank Reconciliation on a weekly basis for a different property.\n2. Responsible for incoming payment, deposit, and receipt entry.\n3. Working on Payroll and monthly schedules related to financial activity.\n4. Working on the monthly financial report for the client.\nN R Institute of Business Management (GLS MBA) \n5. Accuracy of Account ledger reconciliation\n6. Prepare and review month-end management accounts for clients."}, {"CompanyName": "Paperchase Accountancy India Pvt. limited ", "Role": "Account <PERSON><PERSON><PERSON>", "StartYear": "June \n2019", "EndYear": "'Aug 2022'", "Description/Responsibility": "1. Bank entries and bank reconciliation. \n2. Accuracy of supplier ledger reconciliation. \n3. To procure SOA statements from vendors \n4. Approval pending and credit pending clearance. \n5. Accuracy of supplier payment \n6. Assist to Prepare and review month-end management accounts for clients. \n7. Manage relationships with suppliers and service providers. \n8. Review and maintain aged debtor’s & creditors’ reports weekly, and ensure overdue \nbalances are followed-up.\n 9. Ensure the bank account is reconciled weekly and queries are followed up on time. \n10. Ensure petty cash accounts are reconciled weekly. \n11. Ensure creditor’s accounts are reconciled monthly. \n12. Ensure Creditors’ and Debtors’ account details are up to date. \n13. Keep track, process, and reconcile payments and expenditures, including payroll, \npurchase orders, invoices, statements, checks, refund requisitions, etc, in compliance \nwith financial policies and procedures. \n14. Prepare supplier payment list for client approval. \n15. Review the Reconciliation of Bank Accounts and follow up on outstanding payments.\n16. Review the Reconciliation of Creditors’ accounts and follow up on missing invoices,\n statements, and/or credit notes \n17. Verify and validate creditors’ bank account details with the BACS bureau. \n18. Respond to requests from the clients and back-office team in a timely manner. \n19. Ensure petty cash accounts are reconciled monthly and missing receipts are followed\nup with the operational team. \n20. Ensure credit card and debit card accounts are reconciled monthly and missing \nreceipts are followed-up with the operational team. \n21. Ensure expense claims are processed and paid per the agreed timetable. \n22. Ensure weekly invoices/statements/credit notes/delivery notes/Credit and/or Debit \ncard receipts/Expense Claims are received. "}, {"CompanyName": "Finar Limited", "Role": " Account Assistant", "StartYear": "Aug 2016 ", "EndYear": "'May 2019'", "Description/Responsibility": "1. Preparing daily sales outstanding and sending them to the above GMs level \n2. Responsible for the issuance of credit and debit notes to customers \n3. Entries in SAP of banking, incoming payment, deposit, and journal entry in daily \nroutine work.\nN R Institute of Business Management (GLS MBA) \n4. Preparing MIS reports from SAP of daily fund requirement statements and sending \nthem to the management.\n5. Responsible for sending outstanding reports to above ABMs level and respective \ncustomers.\n6. Planning and budgeting of outstanding collection on a monthly basis and set as a \ntarget to be given to all ABM levels.\n7. Assist to Manager/Sr. Executive in daily activities and all system work.\n8. Follow up on collection with parties and the sales team."}, {"CompanyName": "HDFC BANK LTD ", "Role": " Branch sales officer", "StartYear": "September 2014", "EndYear": "'December\n'2015", "Description/Responsibility": "1. Quality acquisition of Current Account &Saving Account for Resident / Non-resident \nIndian \n2. Ensure quality sourcing of new customers to sell different banking products like \nDemat Accounts, Life Insurance, Credit Card, etc. \n3. Penetration of Fixed Deposit to existing &new customers \n4. Generate leads of customers through referrals, meeting branch walk-ins customers, \nAssociates, e-mailing, direct mailing, ATMs &cold calls in the catchment area. \n5. Ensure quality customer service is delivered.\n6. Meeting productivity norms as defined through the support of channels &own efforts \n7. Strictly adhere to & maintain KYC norms and compliance. \n8. Adhere to the norms, regulations &practices of banks religiously "}], "Skills": ["Software Packages: MS Office, Tally, SPSS, SAP (Business One), Access Dimension,Zenark, Yardi."], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": ["English, Hindi, Gujarati "], "Projects": [{"ProjectName": "1. Financial Analysis of GIPCL (Gujarat industries power com. Ltd) \n2. Project on Organization Strategy of Berger Paints, Ahmadabad. \n3. Project Report on Tata Steel.\nFinancial Overview and Budgetary control of ONGC. (ONGC Ltd)\n IPO is an investment avenue: perception of Indian retail investors.", "Description": "", "TechnologiesUsed": [""], "Role": ""}]}}