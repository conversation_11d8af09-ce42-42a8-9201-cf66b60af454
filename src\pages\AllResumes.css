/* AllResumes.css */
.all-resumes-page {
  animation: fadeIn 0.5s ease-in;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  color: var(--text-primary);
  margin-bottom: 8px;
}

.page-header p {
  color: var(--text-secondary);
  text-align: center !important;
  width: 100%;
  display: block;
  margin: 0;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  text-align: center;
}

.loading-spinner.large {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px 24px;
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.search-control {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-control .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.search-control .search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.search-control .search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-control .search-input::placeholder {
  color: var(--text-tertiary);
}

.stats {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.resumes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.resume-card {
  background: var(--bg-primary);
  padding: 24px;
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
}

.resume-card .favorite-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.resume-card .favorite-btn:hover {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  transform: scale(1.1);
}

.resume-card .favorite-btn.favorited {
  color: var(--accent-primary);
  background: var(--bg-tertiary);
}

.resume-card .favorite-btn.favorited:hover {
  color: #dc2626;
  transform: scale(1.1);
}

.resume-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-primary);
}

.resume-card .resume-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 auto 16px;
}

.resume-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.resume-card .title {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0 0 12px 0;
}

.resume-card .location {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

/* Contact Information Styles */
.resume-card .contact-info {
  margin-bottom: 16px;
}

.resume-card .contact-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 6px;
}

/* Resume Detail Sections (Experience, Education, Skills) */
.resume-card .resume-detail-section {
  margin-bottom: 16px;
  text-align: left;
}

.resume-card .detail-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 6px 0;
  text-align: center;
}

.resume-card .detail-value {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0;
  text-align: center;
  line-height: 1.4;
}

.resume-card .skills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
  margin-top: 6px;
}

.resume-card .skill-tag {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--border-primary);
}

.resume-card .actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.resume-card .no-skills {
  color: var(--text-tertiary);
  font-style: italic;
  font-size: 12px;
}

/* Added Date Styling */
.resume-card .added-date-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: var(--text-tertiary);
  font-size: 12px;
  font-weight: 500;
  background: var(--bg-tertiary);
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

.resume-card .added-date {
  margin: 0;
}

/* Error and Empty States */
.error-message {
  background: var(--bg-primary);
  border: 1px solid var(--error);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  margin-bottom: 24px;
}

.error-message p {
  color: var(--error);
  margin: 0 0 16px 0;
  font-weight: 500;
}

.empty-state {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 48px 24px;
  text-align: center;
  color: var(--text-secondary);
}

.empty-state svg {
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-state h3 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

/* Filter Styles */
.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-toggle-btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
}

.filter-chevron {
  transition: transform 0.2s ease;
}

.filter-chevron.rotated {
  transform: rotate(180deg);
}

.filter-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-sm);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.custom-date-row {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  margin-top: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.filter-select,
.filter-date-input {
  padding: 10px 12px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  font-size: 14px;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-select:focus,
.filter-date-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select:hover,
.filter-date-input:hover {
  border-color: var(--accent-primary);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--border-primary);
}

.filter-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* Filter Mode Toggle */
.filter-mode-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.filter-mode-toggle {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-mode-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.toggle-buttons {
  display: flex;
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 4px;
  border: 1px solid var(--border-primary);
}

.toggle-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-btn.active {
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.toggle-btn:hover:not(.active) {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Filter Context Display */
.filter-context {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.filter-context-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.context-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.filter-mode-indicator {
  font-size: 12px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-primary);
}

.filter-context-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  background: var(--accent-primary);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .controls-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .controls-right {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .search-control {
    max-width: none;
  }

  .resumes-grid {
    grid-template-columns: 1fr;
  }

  .resume-card .actions {
    flex-direction: column;
  }

  .refresh-btn {
    justify-content: center;
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .custom-date-row {
    grid-template-columns: 1fr;
  }

  .filter-actions {
    justify-content: center;
  }

  .filter-mode-toggle {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .toggle-buttons {
    justify-content: center;
  }

  .filter-context-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filter-context-tags {
    justify-content: center;
  }
}
