from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
from datetime import datetime, timedelta
import uuid
import hashlib
import shutil
from pathlib import Path
from extractDataParallel import CProcessDocument
from helperMongoDb import MongoDBClient, COLLECTION_NAME
from openai import OpenAI
from helpers import ReadTxtFile, LoadJsonFile
from fastapi.encoders import jsonable_encoder
from bson import ObjectId
import logging
import tempfile
import asyncio
import threading

# Import the document processor from mongoDBInsertion.py
try:
    from mongoDBInsertion import DocumentProcessor
    DOCUMENT_PROCESSOR_AVAILABLE = True
    print("✅ Document processor imported successfully")
except ImportError as e:
    DOCUMENT_PROCESSOR_AVAILABLE = False
    print(f"❌ Document processor import failed: {e}")
    print("📁 Using legacy processing fallback")

app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

# Setup logging
log_dir = "./logs"
log_file = os.path.join(log_dir, "app.log")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    filemode="a",
)
logger = logging.getLogger(__name__)

# Initialize OpenAI client
openAiClient = OpenAI()

# Mock data storage (in production, use a proper database)
resumes_data = []
activity_log = []

# Activity log collection name
ACTIVITY_COLLECTION = "activity_log"
SEARCH_HISTORY_COLLECTION = "search_history"
NOTIFICATIONS_COLLECTION = "notifications"

def load_activity_log_from_db():
    """Load activity log from MongoDB"""
    global activity_log
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[ACTIVITY_COLLECTION]
            activities = list(collection.find({}).sort("timestamp", -1).limit(1000))  # Get last 1000 activities

            # Convert MongoDB documents to the expected format
            activity_log = []
            for activity in activities:
                action = activity.get('action', '')
                activity_type = determine_activity_type(action)

                activity_log.append({
                    'timestamp': activity.get('timestamp', datetime.now().isoformat()),
                    'action': action,
                    'detail': activity.get('details', ''),  # Convert 'details' to 'detail' for consistency
                    'id': str(activity.get('_id', '')),
                    'user': activity.get('user', 'Admin'),
                    'type': activity_type,
                    'time_ago': 'Just now'
                })

            print(f"📊 Loaded {len(activity_log)} activities from MongoDB")
            return True
    except Exception as e:
        print(f"⚠️ Error loading activity log from MongoDB: {e}")
        return False
    return False

def save_activity_to_db(action, details="", user="Admin"):
    """Save activity to MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[ACTIVITY_COLLECTION]
            activity_doc = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'details': details,
                'user': user,
                'created_at': datetime.now()
            }

            collection.insert_one(activity_doc)
            print(f"💾 Saved activity to MongoDB: {action}")
            return True
    except Exception as e:
        print(f"⚠️ Error saving activity to MongoDB: {e}")
        return False
    return False

def save_search_history_to_db(search_item):
    """Save search history item to MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[SEARCH_HISTORY_COLLECTION]
            search_doc = {
                **search_item,
                'created_at': datetime.now()
            }

            result = collection.insert_one(search_doc)
            print(f"💾 Saved search history to MongoDB: {search_item.get('query', '')}")
            return str(result.inserted_id)
    except Exception as e:
        print(f"⚠️ Error saving search history to MongoDB: {e}")
        return None
    return None

def load_search_history_from_db(limit=50):
    """Load search history from MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[SEARCH_HISTORY_COLLECTION]
            history = list(collection.find({}).sort("timestamp", -1).limit(limit))

            # Convert MongoDB documents to the expected format
            search_history = []
            for item in history:
                # Ensure we have a consistent ID for deletion purposes
                item_id = item.get('id')
                if not item_id:
                    item_id = str(item.get('_id', ''))

                search_history.append({
                    'id': item_id,
                    'query': item.get('query', ''),
                    'timestamp': item.get('timestamp', ''),
                    'date': item.get('date', ''),
                    'time': item.get('time', ''),
                    'resultsCount': item.get('resultsCount', 0),
                    'gptResponse': item.get('gptResponse', ''),
                    'mongoQuery': item.get('mongoQuery', []),
                    'results': item.get('results', [])
                })

            print(f"📊 Loaded {len(search_history)} search history items from MongoDB")
            print(f"🔍 Debug: Sample IDs from loaded items: {[item['id'][:20] + '...' if len(item['id']) > 20 else item['id'] for item in search_history[:3]]}")
            return search_history
    except Exception as e:
        print(f"⚠️ Error loading search history from MongoDB: {e}")
        return []
    return []

def delete_search_history_from_db(item_id=None):
    """Delete search history from MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[SEARCH_HISTORY_COLLECTION]

            if item_id:
                # Try to delete by custom 'id' field first
                print(f"🔍 Attempting to delete search history item with id: {item_id}")
                result = collection.delete_one({"id": item_id})

                if result.deleted_count > 0:
                    print(f"🗑️ Deleted search history item by custom id: {item_id}")
                    return True
                else:
                    # If not found by custom id, try by MongoDB _id field
                    print(f"⚠️ Item not found by custom id, trying MongoDB _id: {item_id}")
                    try:
                        from bson import ObjectId
                        # Try to convert to ObjectId if it's a valid ObjectId string
                        if ObjectId.is_valid(item_id):
                            result = collection.delete_one({"_id": ObjectId(item_id)})
                            if result.deleted_count > 0:
                                print(f"🗑️ Deleted search history item by MongoDB _id: {item_id}")
                                return True
                    except Exception as oid_error:
                        print(f"⚠️ Error trying ObjectId conversion: {oid_error}")

                    # Final attempt: try as string _id
                    result = collection.delete_one({"_id": item_id})
                    if result.deleted_count > 0:
                        print(f"🗑️ Deleted search history item by string _id: {item_id}")
                        return True

                    print(f"❌ Search history item not found with any id format: {item_id}")
                    # Debug: Show what items exist in the collection
                    existing_items = list(collection.find({}, {"id": 1, "_id": 1}).limit(5))
                    print(f"🔍 Debug: First 5 items in collection: {existing_items}")
                    return False
            else:
                # Delete all search history
                result = collection.delete_many({})
                print(f"🗑️ Deleted {result.deleted_count} search history items")
                return True
    except Exception as e:
        print(f"⚠️ Error deleting search history from MongoDB: {e}")
        return False
    return False

def save_notification_to_db(notification_type, title, message, details=None):
    """Save notification to MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            notification_doc = {
                'id': str(uuid.uuid4()),
                'type': notification_type,  # 'upload', 'duplicate', 'error', 'search', 'no_results'
                'title': title,
                'message': message,
                'details': details or {},
                'unread': True,
                'timestamp': datetime.now().isoformat(),
                'created_at': datetime.now()
            }

            result = collection.insert_one(notification_doc)
            print(f"🔔 Saved notification to MongoDB: {title}")
            return str(result.inserted_id)
    except Exception as e:
        print(f"⚠️ Error saving notification to MongoDB: {e}")
        return None
    return None

def load_notifications_from_db(limit=50):
    """Load notifications from MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            notifications = list(collection.find({}).sort("timestamp", -1).limit(limit))

            # Convert MongoDB documents to the expected format
            notification_list = []
            for notif in notifications:
                notification_list.append({
                    'id': notif.get('id', str(notif.get('_id', ''))),
                    'type': notif.get('type', 'info'),
                    'title': notif.get('title', ''),
                    'message': notif.get('message', ''),
                    'details': notif.get('details', {}),
                    'unread': notif.get('unread', True),
                    'timestamp': notif.get('timestamp', ''),
                    'time_ago': format_time_ago(notif.get('timestamp', ''))
                })

            print(f"🔔 Loaded {len(notification_list)} notifications from MongoDB")
            return notification_list
    except Exception as e:
        print(f"⚠️ Error loading notifications from MongoDB: {e}")
        return []
    return []

def mark_notification_read(notification_id):
    """Mark a notification as read in MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            result = collection.update_one(
                {"id": notification_id},
                {"$set": {"unread": False}}
            )
            if result.modified_count > 0:
                print(f"🔔 Marked notification as read: {notification_id}")
                return True
    except Exception as e:
        print(f"⚠️ Error marking notification as read: {e}")
    return False

def mark_all_notifications_read():
    """Mark all notifications as read in MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            result = collection.update_many(
                {"unread": True},
                {"$set": {"unread": False}}
            )
            print(f"🔔 Marked {result.modified_count} notifications as read")
            return result.modified_count
    except Exception as e:
        print(f"⚠️ Error marking all notifications as read: {e}")
    return 0

def delete_notification_from_db(notification_id):
    """Delete a notification from MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            result = collection.delete_one({"id": notification_id})
            if result.deleted_count > 0:
                print(f"🔔 Deleted notification: {notification_id}")
                return True
    except Exception as e:
        print(f"⚠️ Error deleting notification: {e}")
    return False

def clear_all_notifications():
    """Clear all notifications from MongoDB"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[NOTIFICATIONS_COLLECTION]
            result = collection.delete_many({})
            print(f"🔔 Cleared {result.deleted_count} notifications")
            return result.deleted_count
    except Exception as e:
        print(f"⚠️ Error clearing notifications: {e}")
    return 0

def format_time_ago(timestamp_str):
    """Format timestamp to relative time"""
    try:
        if not timestamp_str:
            return 'Unknown time'

        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        now = datetime.now()

        # Make both timezone-aware or timezone-naive
        if timestamp.tzinfo is not None and now.tzinfo is None:
            now = now.replace(tzinfo=timestamp.tzinfo)
        elif timestamp.tzinfo is None and now.tzinfo is not None:
            timestamp = timestamp.replace(tzinfo=now.tzinfo)

        diff = now - timestamp

        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} min ago"
        else:
            return "Just now"
    except Exception as e:
        print(f"Error formatting time: {e}")
        return "Unknown time"

def get_person_name_from_filename(filename):
    """Extract person name from filename for notifications"""
    try:
        if not filename:
            return "Unknown Person"

        # Remove file extension
        name_part = Path(filename).stem

        # Replace common separators with spaces
        name_part = name_part.replace('_', ' ').replace('-', ' ').replace('.', ' ')

        # Remove common resume keywords
        keywords_to_remove = ['resume', 'cv', 'curriculum', 'vitae', 'updated', 'new', 'latest', 'final']
        words = name_part.split()
        filtered_words = [word for word in words if word.lower() not in keywords_to_remove]

        if filtered_words:
            # Take first 2-3 words as name
            name = ' '.join(filtered_words[:3])
            # Capitalize each word
            name = ' '.join(word.capitalize() for word in name.split())
            return name
        else:
            # Fallback to filename without extension
            return name_part.title()

    except Exception as e:
        print(f"Error extracting name from filename {filename}: {e}")
        return "Unknown Person"

# Configuration
UPLOAD_FOLDER = './Data/inputData/Resume_Schema'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Initialize MongoDB client
try:
    mongo_client = MongoDBClient()
    MONGODB_AVAILABLE = True
    print(f"✅ MongoDB connected successfully to {mongo_client.db.name}")
except Exception as e:
    mongo_client = None
    MONGODB_AVAILABLE = False
    print(f"❌ MongoDB connection failed: {e}")
    print("📁 Using sample data fallback")



def calculate_checksum(file_data: bytes) -> str:
    """Calculate SHA-256 checksum from bytes."""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(file_data)
    return sha256_hash.hexdigest()

def read_json_file(file_path):
    """Read and return JSON data from file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        logger.error(f"Error reading JSON file {file_path}: {e}")
        return {}

def load_sample_resumes():
    """Load sample resume data for fallback."""
    return [
        {
            "_id": "sample_1",
            "Resume": {
                "PersonalInformation": {
                    "FullName": "John Doe",
                    "Email": "<EMAIL>",
                    "ContactNumber": "+1-555-0123",
                    "Address": "123 Main St, City, State"
                },
                "Skills": ["Python", "JavaScript", "MongoDB", "React"],
                "WorkExperience": [
                    {
                        "CompanyName": "Tech Corp",
                        "Role": "Software Engineer",
                        "StartYear": 2020,
                        "EndYear": "Current"
                    }
                ]
            }
        }
    ]

def processQuery(naturalQuery: str) -> str:
    """
    Converts a natural language query into a MongoDB pipeline using OpenAI.

    Args:
        naturalQuery (str): The natural language query provided by the user.

    Returns:
        str: The generated MongoDB pipeline as JSON string.

    Raises:
        Exception: If there is an error in generating or parsing the AI response.
    """
    logger.info("Processing natural language query: %s", naturalQuery)
    print(f"🔍 DEBUG: Processing query with OpenAI: {naturalQuery}")

    try:
        # Read system prompt and sample resume data
        strSystemPromptInputPart1 = ReadTxtFile("resource/strSystemPromptInput.txt")
        FILE_PATH = "resource/SampleResume.json"
        resume_data = read_json_file(FILE_PATH)
        strSystemPromptInput = strSystemPromptInputPart1 + str(resume_data)

        responseFormatInput = LoadJsonFile("resource/strResponseFormatInput.json")

        logger.info("Sending query to OpenAI for processing.")
        print(f"🤖 DEBUG: Calling OpenAI API...")

        completion = openAiClient.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": strSystemPromptInput},
                {"role": "user", "content": naturalQuery}
            ],
            temperature=0.7,
            response_format=responseFormatInput
        )

        logger.info("Received response from OpenAI.")
        print(f"✅ DEBUG: OpenAI response received")

        # Parse the response
        responseContent = json.loads(completion.choices[0].message.content)
        strMongoDbQuery = responseContent["MongoDbQuery"]

        logger.info("Generated MongoDB query: %s", strMongoDbQuery)
        print(f"🔍 DEBUG: Generated MongoDB pipeline: {strMongoDbQuery}")

        return strMongoDbQuery

    except Exception as e:
        logger.error("Error generating MongoDB query with OpenAI: %s", e)
        print(f"❌ DEBUG: OpenAI error: {e}")

        # Fallback to simple keyword-based search
        print(f"🔄 DEBUG: Falling back to keyword-based search")
        return _fallback_keyword_search(naturalQuery)

def _fallback_keyword_search(naturalQuery: str) -> str:
    """Fallback keyword-based search when OpenAI fails."""
    try:
        # Clean and process the query
        keywords = [word.strip().lower() for word in naturalQuery.lower().split() if len(word.strip()) > 2]

        # Filter out common words
        stop_words = {'the', 'and', 'with', 'for', 'are', 'have', 'has', 'will', 'can', 'who', 'what', 'where', 'when', 'how', 'find', 'show', 'get', 'years', 'year', 'experience', 'work', 'job', 'position', 'role', 'candidate', 'person', 'people', 'looking', 'seeking'}
        keywords = [word for word in keywords if word not in stop_words and len(word) > 2]

        # Create a basic pipeline
        pipeline = [{"$match": {"Resume": {"$exists": True}}}]

        if keywords:
            keyword_conditions = []
            for keyword in keywords:
                keyword_or_conditions = [
                    {"Resume.PersonalInformation.FullName": {"$regex": f"\\b{keyword}", "$options": "i"}},
                    {"Resume.Skills": {"$regex": keyword, "$options": "i"}},
                    {"Resume.WorkExperience.Role": {"$regex": keyword, "$options": "i"}},
                    {"Resume.WorkExperience.CompanyName": {"$regex": keyword, "$options": "i"}},
                    {"Resume.Education.Degree": {"$regex": keyword, "$options": "i"}},
                    {"Resume.Education.Institution": {"$regex": keyword, "$options": "i"}},
                    {"Resume.Objective": {"$regex": keyword, "$options": "i"}},
                    {"Resume.PersonalInformation.Address": {"$regex": keyword, "$options": "i"}}
                ]
                keyword_conditions.append({"$or": keyword_or_conditions})

            if keyword_conditions:
                if len(keyword_conditions) == 1:
                    pipeline.append({"$match": keyword_conditions[0]})
                else:
                    pipeline.append({"$match": {"$and": keyword_conditions}})

        pipeline.append({"$limit": 50})
        return json.dumps(pipeline)

    except Exception as e:
        logger.error(f"Error in fallback search: {e}")
        # Return a query that matches nothing instead of everything
        return '[{"$match": {"_id": {"$exists": false}}}]'

def load_query_string(query_string: str):
    """Parse pipeline string into list, handling both JSON and Python literal formats."""
    import ast
    try:
        # First try to parse as JSON (works for double quotes)
        result = json.loads(query_string)
    except json.JSONDecodeError:
        try:
            # If JSON fails, try ast.literal_eval (works for single quotes)
            result = ast.literal_eval(query_string)
        except (ValueError, SyntaxError) as e:
            logger.error(f"Invalid query string format: {e}")
            # Return a query that matches nothing instead of everything
            return [{"$match": {"_id": {"$exists": False}}}]
    return result

def getOutputQueryText(naturalQuery: str, listPipeline: list, result: list) -> str:
    """
    Converts MongoDB output into a human-readable explanation using AI.

    Args:
        naturalQuery (str): The original natural language query.
        listPipeline (list): The generated MongoDB pipeline.
        result (list): The output results from the MongoDB query.

    Returns:
        str: A human-readable explanation of the MongoDB query result.

    Raises:
        Exception: If there is an error in processing the AI output response.
    """
    logger.info("Generating textual explanation for MongoDB output.")
    print(f"🤖 DEBUG: Generating AI explanation for {len(result)} results")

    try:
        strSystemPromptOutput = ReadTxtFile("resource/strSystemPromptOutput.txt")
        strUserPromptOutput = (
            f"Natural Question: {naturalQuery},\n"
            f"MongoDB Query: {listPipeline},\n"
            f"MongoDB Output: {result}"
        )

        # Truncate if too long
        if len(strUserPromptOutput) > 20000:
            strUserPromptOutput = strUserPromptOutput[:20000]

        responseFormatOutput = LoadJsonFile("resource/strResponseFormatOutput.json")

        completion = openAiClient.chat.completions.create(
            model="gpt-4.1",
            messages=[
                {"role": "system", "content": strSystemPromptOutput},
                {"role": "user", "content": strUserPromptOutput}
            ],
            temperature=0.7,
            response_format=responseFormatOutput
        )

        logger.info("Received textual explanation from OpenAI.")
        print(f"✅ DEBUG: AI explanation generated")

        responseContent = json.loads(completion.choices[0].message.content)
        strOutputText = responseContent["SqlQueryOutputText"]
        return strOutputText

    except Exception as e:
        logger.error("Error generating output query text with OpenAI: %s", e)
        print(f"❌ DEBUG: AI explanation error: {e}")
        # Fallback to simple message
        return f"Found {len(result)} resumes matching your query: '{naturalQuery}'"

def search_sample_data(naturalQuery: str):
    """Search sample data when MongoDB is not available."""
    sample_data = load_sample_resumes()
    return {
        "query": [{"$match": {"$text": {"$search": naturalQuery}}}],
        "listOfDict": sample_data,
        "result": f"Showing {len(sample_data)} sample resumes (MongoDB not available)"
    }

def get_person_name_from_filename(filename):
    """Extract person name from processed resume in MongoDB by filename"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[COLLECTION_NAME]

            # Search for the resume by original filename
            resume = collection.find_one({"original_filename": filename})
            if resume and "Resume" in resume:
                resume_data = resume["Resume"]
                personal_info = resume_data.get("PersonalInformation", {})

                # Try different name fields
                name = (personal_info.get("FullName") or
                       personal_info.get("Name") or
                       personal_info.get("FirstName", "") + " " + personal_info.get("LastName", "")).strip()

                if name:
                    return name

        # Fallback: extract name from filename
        name_from_file = Path(filename).stem.replace('_', ' ').replace('-', ' ')
        # Capitalize each word
        return ' '.join(word.capitalize() for word in name_from_file.split())

    except Exception as e:
        print(f"Error extracting name from {filename}: {e}")
        # Fallback: extract name from filename
        name_from_file = Path(filename).stem.replace('_', ' ').replace('-', ' ')
        return ' '.join(word.capitalize() for word in name_from_file.split())

def determine_activity_type(action):
    """Determine activity type based on action string"""
    action_lower = action.lower()
    if 'duplicate' in action_lower:
        return 'duplicate'
    elif 'upload' in action_lower:
        return 'upload'
    elif 'search' in action_lower:
        return 'search'
    elif 'process' in action_lower or 'bulk' in action_lower:
        return 'process'
    elif 'delete' in action_lower:
        return 'delete'
    elif 'download' in action_lower:
        return 'download'
    elif 'export' in action_lower:
        return 'export'
    elif 'system' in action_lower or 'start' in action_lower:
        return 'system'
    elif 'error' in action_lower:
        return 'error'
    else:
        return 'default'

def log_activity(action, detail, user="Admin"):
    """Log an activity to both memory and MongoDB for persistence"""
    activity_type = determine_activity_type(action)

    activity = {
        'id': str(uuid.uuid4()),
        'action': action,
        'detail': detail,
        'user': user,
        'type': activity_type,
        'timestamp': datetime.now().isoformat(),
        'time_ago': 'Just now'
    }

    # Add to memory (for immediate access)
    activity_log.insert(0, activity)  # Add to beginning
    # Keep only last 100 activities in memory
    if len(activity_log) > 100:
        activity_log.pop()

    # Save to MongoDB for persistence
    save_activity_to_db(action, detail, user)

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Resume AI Agent API is running',
        'mongodb_available': MONGODB_AVAILABLE,
        'database': mongo_client.db.name if MONGODB_AVAILABLE and mongo_client else 'Not connected'
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'message': 'Resume AI Agent API is running'})

@app.route('/api/upload', methods=['POST'])
def upload_resume():
    """Upload and process resume files - legacy endpoint for PDF-only uploads"""
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400

        files = request.files.getlist('files')
        duplicate_files = []
        saved_files = []

        for file in files:
            if file.filename == '':
                continue

            # Validate file type
            if not file.filename.lower().endswith('.pdf'):
                return jsonify({'error': f'File {file.filename} is not a PDF'}), 400

            try:
                # Read file data
                file_data = file.read()
                checksum = calculate_checksum(file_data)

                # Check for duplicates using MongoDB
                if MONGODB_AVAILABLE and mongo_client:
                    if mongo_client.is_duplicate(checksum):
                        duplicate_files.append(file.filename)
                        continue

                # Save file to upload directory (use original filename, not UUID)
                file_path = Path(UPLOAD_FOLDER) / file.filename
                with open(file_path, "wb") as f:
                    f.write(file_data)

                saved_files.append(str(file_path))

                # Log activity with person name
                person_name = get_person_name_from_filename(file.filename)
                log_activity('Resume uploaded', person_name, 'Admin')

            except Exception as e:
                print(f"Error processing file {file.filename}: {e}")
                return jsonify({'error': f'Error processing {file.filename}: {str(e)}'}), 500

        # Process saved files using the same pipeline as frontendV3.py
        if saved_files:
            try:
                # Initialize the document processor
                processor = CProcessDocument(strVendorName="Resume_Schema")

                # Process the uploaded documents
                processor.MprocessAllDocuments(iFilesToProcess=len(saved_files))

                log_activity('Documents processed', f'{len(saved_files)} files processed successfully', 'System')

            except Exception as e:
                print(f"Error during document processing: {e}")
                # Don't fail the upload if processing fails, just log it
                log_activity('Processing error', f'Error processing documents: {str(e)}', 'System')

        return jsonify({
            'message': f'Successfully processed {len(saved_files)} files',
            'processed_files': len(saved_files),
            'duplicate_files': len(duplicate_files),
            'duplicates': duplicate_files,
            'saved_files': [Path(f).name for f in saved_files],
            'mongodb_available': MONGODB_AVAILABLE
        })

    except Exception as e:
        print(f"Upload endpoint error: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

def run_async_processing(temp_folder_path, n_workers=4):
    """Run async processing in a separate thread with its own event loop"""
    def run_in_thread():
        # Create new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Initialize document processor
            processor = DocumentProcessor(
                database_name="dbProductionV2",
                collection_name="collectionResumeV2",
                n_workers=n_workers
            )
            # Process the folder
            result = loop.run_until_complete(processor.process_folder(temp_folder_path))
            return result
        except Exception as e:
            print(f"Error in async processing: {e}")
            # Return a basic result structure on error
            return {
                "processed_successfully": 0,
                "failed_files": 1,
                "duplicate_files": 0,
                "error": str(e)
            }
        finally:
            try:
                # Cancel all pending tasks
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()
                # Wait for tasks to complete cancellation
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception as cleanup_error:
                print(f"Error during cleanup: {cleanup_error}")
            finally:
                loop.close()

    # Run in a daemon thread to prevent hanging on shutdown
    import threading
    result_container = [None]
    exception_container = [None]

    def thread_target():
        try:
            result_container[0] = run_in_thread()
        except Exception as e:
            exception_container[0] = e
            result_container[0] = {
                "processed_successfully": 0,
                "failed_files": 1,
                "duplicate_files": 0,
                "error": str(e)
            }

    thread = threading.Thread(target=thread_target, daemon=True)
    thread.start()
    thread.join(timeout=300)  # 5 minute timeout

    if thread.is_alive():
        print("Processing thread timed out")
        return {
            "processed_successfully": 0,
            "failed_files": 1,
            "duplicate_files": 0,
            "error": "Processing timed out"
        }

    if exception_container[0]:
        print(f"Thread exception: {exception_container[0]}")

    return result_container[0] or {
        "processed_successfully": 0,
        "failed_files": 1,
        "duplicate_files": 0,
        "error": "Unknown processing error"
    }

@app.route('/api/upload-advanced', methods=['POST'])
def upload_resume_advanced():
    """
    Advanced upload endpoint that supports multiple file formats (PDF, DOC, DOCX, Images)
    and uses the mongoDBInsertion.py processing pipeline
    """
    try:
        if not DOCUMENT_PROCESSOR_AVAILABLE:
            return jsonify({
                'error': 'Advanced document processor not available. Please use /api/upload for PDF files only.',
                'fallback_endpoint': '/api/upload'
            }), 503

        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({'error': 'No valid files provided'}), 400

        # Get processing options from request
        n_workers = int(request.form.get('workers', 4))  # Default to 4 workers for web uploads

        # Supported file extensions
        SUPPORTED_EXTENSIONS = {
            '.pdf', '.doc', '.docx',
            '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'
        }

        # Validate files and save to temporary directory
        temp_dir = tempfile.mkdtemp(prefix='upload_')
        saved_files = []
        invalid_files = []

        try:
            print(f"📁 Created temporary directory: {temp_dir}")

            for file in files:
                if file.filename == '':
                    continue

                # Check file extension
                file_ext = Path(file.filename).suffix.lower()
                if file_ext not in SUPPORTED_EXTENSIONS:
                    invalid_files.append({
                        'filename': file.filename,
                        'reason': f'Unsupported file type: {file_ext}'
                    })
                    continue

                # Save file to temporary directory
                file_path = os.path.join(temp_dir, file.filename)
                file.save(file_path)
                saved_files.append(file_path)

                print(f"📄 Saved file: {file.filename}")

            if not saved_files:
                return jsonify({
                    'error': 'No valid files to process',
                    'invalid_files': invalid_files,
                    'supported_formats': list(SUPPORTED_EXTENSIONS)
                }), 400

            # Log activity
            log_activity('Advanced upload started', f'{len(saved_files)} files uploaded for processing')

            # Process files using the document processor
            print(f"🚀 Starting processing with {n_workers} workers...")

            # Run processing in background thread
            processing_result = run_async_processing(temp_dir, n_workers)

            # Log final activity with detailed information
            if processing_result:
                success_count = processing_result.get('processed_successfully', 0)
                failed_count = processing_result.get('failed_files', 0)
                duplicate_count = processing_result.get('duplicate_files', 0)

                # Get detailed lists
                processed_files = processing_result.get('processed_files', [])
                failed_files_list = processing_result.get('failed_files_list', [])
                duplicate_files_list = processing_result.get('duplicate_files_list', [])

                # Log individual successes with person names
                for filename in processed_files:
                    person_name = get_person_name_from_filename(filename)
                    log_activity('Resume processed successfully', person_name, 'System')

                # Log individual failures
                if failed_files_list:
                    for filename in failed_files_list:
                        log_activity('Resume processing failed', filename, 'System')

                # Log individual duplicates with person names
                for filename in duplicate_files_list:
                    person_name = get_person_name_from_filename(filename)
                    log_activity('Duplicate resume detected', person_name, 'System')

                # Log summary
                log_activity(
                    'Advanced processing completed',
                    f'Success: {success_count}, Failed: {failed_count}, Duplicates: {duplicate_count}',
                    'System'
                )

                # Create notifications for upload results
                # Notification for successful uploads
                if success_count > 0:
                    if success_count == 1:
                        # Single file notification with person name
                        successful_file = processed_files[0] if processed_files else "Unknown file"
                        person_name = get_person_name_from_filename(successful_file)
                        save_notification_to_db(
                            'upload',
                            'Resume Uploaded Successfully',
                            f'Resume for {person_name} has been processed and added to the database',
                            {'file_name': successful_file, 'person_name': person_name}
                        )
                    else:
                        # Multiple files notification
                        save_notification_to_db(
                            'upload',
                            'Multiple Resumes Uploaded',
                            f'{success_count} resumes have been successfully processed and added to the database',
                            {'processed_count': success_count}
                        )

                # Notification for duplicate files
                if duplicate_count > 0:
                    for filename in duplicate_files_list:
                        person_name = get_person_name_from_filename(filename)
                        save_notification_to_db(
                            'duplicate',
                            'Duplicate Resume Detected',
                            f'Resume for {person_name} already exists in the database and was skipped',
                            {'file_name': filename, 'person_name': person_name}
                        )

                # Notification for failed files
                if failed_count > 0:
                    for filename in failed_files_list:
                        save_notification_to_db(
                            'error',
                            'Resume Processing Failed',
                            f'Failed to process {filename}. Please try uploading again or contact support.',
                            {'file_name': filename}
                        )

            return jsonify({
                'message': 'File processing completed',
                'processing_result': processing_result,
                'uploaded_files': len(saved_files),
                'invalid_files': invalid_files,
                'temp_directory': temp_dir,
                'workers_used': n_workers,
                'mongodb_available': MONGODB_AVAILABLE,
                'processor_available': DOCUMENT_PROCESSOR_AVAILABLE
            })

        finally:
            # Clean up temporary directory
            try:
                shutil.rmtree(temp_dir)
                print(f"🗑️ Cleaned up temporary directory: {temp_dir}")
            except Exception as cleanup_error:
                print(f"⚠️ Warning: Could not clean up temp directory {temp_dir}: {cleanup_error}")

    except Exception as e:
        print(f"Advanced upload endpoint error: {e}")
        log_activity('Advanced upload error', f'Error: {str(e)}')
        return jsonify({'error': f'Advanced upload failed: {str(e)}'}), 500

@app.route('/api/upload-simple', methods=['POST'])
def upload_resume_simple():
    """
    Simple upload endpoint that saves files to MongoDB without AWS processing
    This is a fallback for when AWS processing fails
    """
    try:
        if 'files' not in request.files:
            return jsonify({'error': 'No files provided'}), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({'error': 'No valid files provided'}), 400

        # Supported file extensions
        SUPPORTED_EXTENSIONS = {'.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'}

        saved_files = []
        duplicate_files = []
        invalid_files = []

        for file in files:
            if file.filename == '':
                continue

            # Check file extension
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in SUPPORTED_EXTENSIONS:
                invalid_files.append({
                    'filename': file.filename,
                    'reason': f'Unsupported file type: {file_ext}'
                })
                continue

            try:
                # Read file data
                file_data = file.read()
                checksum = calculate_checksum(file_data)

                # Check for duplicates using MongoDB
                if MONGODB_AVAILABLE and mongo_client:
                    if mongo_client.is_duplicate(checksum):
                        duplicate_files.append(file.filename)
                        continue

                # Save file to MongoDB GridFS
                if MONGODB_AVAILABLE and mongo_client:
                    file_id = mongo_client.save_file_to_gridfs(
                        file_data,
                        file.filename,
                        checksum
                    )

                    # Create a basic document entry
                    basic_document = {
                        "original_filename": file.filename,
                        "checksum": checksum,
                        "file_id": file_id,
                        "timestamp": datetime.now(),
                        "processing_status": "uploaded_only",
                        "vendor_name": "Resume_Schema"
                    }

                    # Insert basic document
                    mongo_client.db[mongo_client.collection_name].insert_one(basic_document)
                    saved_files.append(file.filename)

                    log_activity('Simple upload', f'File {file.filename} saved to MongoDB')
                else:
                    # Fallback: save to local directory
                    file_path = Path(UPLOAD_FOLDER) / file.filename
                    with open(file_path, "wb") as f:
                        f.write(file_data)
                    saved_files.append(file.filename)
                    log_activity('Simple upload', f'File {file.filename} saved locally')

            except Exception as e:
                print(f"Error processing file {file.filename}: {e}")
                invalid_files.append({
                    'filename': file.filename,
                    'reason': f'Processing error: {str(e)}'
                })

        return jsonify({
            'message': f'Simple upload completed',
            'uploaded_files': len(saved_files),
            'saved_files': saved_files,
            'duplicate_files': len(duplicate_files),
            'duplicates': duplicate_files,
            'invalid_files': invalid_files,
            'mongodb_available': MONGODB_AVAILABLE,
            'note': 'Files uploaded without AI processing. Use advanced upload for full processing.'
        })

    except Exception as e:
        print(f"Simple upload endpoint error: {e}")
        log_activity('Simple upload error', f'Error: {str(e)}')
        return jsonify({'error': f'Simple upload failed: {str(e)}'}), 500

@app.route('/query/', methods=['GET'])
def query_nlp():
    """Process natural language query and return MongoDB results"""
    try:
        natural_query = request.args.get('naturalQuery', '')

        if not natural_query:
            return jsonify({'error': 'naturalQuery parameter is required'}), 400

        if MONGODB_AVAILABLE and mongo_client:
            try:
                logger.info("Processing user query: %s", natural_query)
                print(f"🔍 DEBUG: Starting query processing for: {natural_query}")
                strPipeline = processQuery(natural_query)
                print(f"🔍 DEBUG: Generated pipeline: {strPipeline}")
                listPipeline = load_query_string(strPipeline)
                print(f"🔍 DEBUG: Parsed pipeline: {listPipeline}")
                cursorMongoDb = mongo_client.execute_pipeline(listPipeline)
                result = [doc for doc in cursorMongoDb]
                print(f"🔍 DEBUG: Query result count: {len(result)}")
                strOutputText = getOutputQueryText(natural_query, listPipeline, result)
                return jsonify({
                    "query": listPipeline,
                    "listOfDict": jsonable_encoder(result, custom_encoder={ObjectId: str}),
                    "result": strOutputText
                })
            except Exception as e:
                logger.error("Error processing query: %s", e)
                print(f"🔍 DEBUG: Query processing error: {e}")
                # Fallback to sample data search
                return jsonify(search_sample_data(natural_query))
        else:
            # Use sample data when MongoDB is not available
            return jsonify(search_sample_data(natural_query))

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/search', methods=['POST'])
def search_resumes():
    """Search resumes using natural language query via GPT API"""
    try:
        data = request.get_json()
        query = data.get('query', '')

        if not query:
            return jsonify({'error': 'Query is required'}), 400

        # Use the internal query processing
        try:
            # Use internal query_nlp function
            with app.test_request_context(f'/query/?naturalQuery={query}'):
                result = query_nlp()
                api_data = result.get_json()

            if api_data and not api_data.get('error'):
                # Handle different response formats
                if isinstance(api_data, list):
                    search_results = api_data
                    gpt_response = "Data received successfully."
                    mongo_query = []
                elif isinstance(api_data, dict):
                    search_results = api_data.get("listOfDict", [])
                    gpt_response = api_data.get("result", "Data received successfully.")
                    mongo_query = api_data.get("query", [])
                else:
                    search_results = []
                    gpt_response = "Unexpected API response format."
                    mongo_query = []

                # Log activity
                log_activity('Search completed', f'"{query}" ({len(search_results)} results)', 'Admin')

                # Create notification for search results
                if len(search_results) > 0:
                    save_notification_to_db(
                        'search',
                        'Search Results Available',
                        f'Found {len(search_results)} resume(s) matching your query: "{query}"',
                        {'query': query, 'results_count': len(search_results)}
                    )
                else:
                    save_notification_to_db(
                        'no_results',
                        'No Search Results Found',
                        f'No resumes found matching your query: "{query}". Try using different keywords.',
                        {'query': query, 'results_count': 0}
                    )

                return jsonify({
                    'query': query,
                    'results': search_results,
                    'count': len(search_results),
                    'gpt_response': gpt_response,
                    'mongo_query': mongo_query,
                    'api_status': 'success'
                })
            else:
                # Log activity
                log_activity('Search error', f'"{query}" - Internal query processing failed')

                return jsonify({
                    'query': query,
                    'results': [],
                    'count': 0,
                    'error': api_data.get('error', 'Internal query processing failed'),
                    'api_status': 'internal_error'
                }), 500

        except Exception as e:
            # Log activity
            log_activity('Search error', f'"{query}" - Processing error: {str(e)}')

            # Create notification for search error
            save_notification_to_db(
                'error',
                'Search Processing Error',
                f'Error processing search query: "{query}". Using fallback search.',
                {'query': query, 'error': str(e)}
            )

            # Fallback to mock data if API is unavailable
            filtered_resumes = []
            for resume in resumes_data:
                # Simple keyword matching as fallback
                if (query.lower() in resume['name'].lower() or
                    query.lower() in resume['title'].lower() or
                    any(query.lower() in skill.lower() for skill in resume['skills']) or
                    query.lower() in resume['location'].lower()):

                    resume_copy = resume.copy()
                    resume_copy['score'] = 85  # Lower score for fallback
                    filtered_resumes.append(resume_copy)

            # Create notification for fallback search results
            if len(filtered_resumes) > 0:
                save_notification_to_db(
                    'search',
                    'Fallback Search Results',
                    f'Found {len(filtered_resumes)} resume(s) using fallback search for: "{query}"',
                    {'query': query, 'results_count': len(filtered_resumes), 'search_type': 'fallback'}
                )
            else:
                save_notification_to_db(
                    'no_results',
                    'No Results Found',
                    f'No resumes found matching your query: "{query}". Try different keywords.',
                    {'query': query, 'results_count': 0, 'search_type': 'fallback'}
                )

            return jsonify({
                'query': query,
                'results': filtered_resumes,
                'count': len(filtered_resumes),
                'gpt_response': f'Using fallback search due to API connection error: {str(e)}',
                'mongo_query': [],
                'api_status': 'fallback'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/showall', methods=['GET'])
def show_all():
    """Get all resumes from MongoDB or fallback to sample data."""
    from helperMongoDb import DATABASE_NAME, COLLECTION_NAME
    print(f"🔍 DEBUG: Using database: {DATABASE_NAME}, collection: {COLLECTION_NAME}")
    print(f"🔍 DEBUG: MONGODB_AVAILABLE: {MONGODB_AVAILABLE}")
    print(f"🔍 DEBUG: mongo_client: {mongo_client}")

    if MONGODB_AVAILABLE and mongo_client:
        try:
            print(f"🔍 DEBUG: mongo_client.db.name: {mongo_client.db.name}")
            cursorShowAll = mongo_client.get_all_resume_data()
            lsShowAll = [doc for doc in cursorShowAll]

            if not lsShowAll:
                print("📁 No resumes in MongoDB, using sample data")
                return jsonify(load_sample_resumes())

            print(f"📊 Loaded {len(lsShowAll)} resumes from MongoDB database: {mongo_client.db.name}")
            return jsonify(jsonable_encoder(lsShowAll, custom_encoder={ObjectId: str}))
        except Exception as e:
            print(f"⚠️ MongoDB error: {e}, using sample data")
            return jsonify(load_sample_resumes())
    else:
        print("📁 Using sample data (MongoDB not available)")
        return jsonify(load_sample_resumes())

@app.route('/api/resumes', methods=['GET'])
def get_all_resumes():
    """Get all resumes from MongoDB database with pagination support"""
    try:
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))

        # Get all resumes using internal function
        try:
            # Use the internal show_all function directly
            response_obj = show_all()
            all_resumes = response_obj.get_json()

            if all_resumes and len(all_resumes) > 0:
                total_count = len(all_resumes)

                # Apply pagination
                start_index = (page - 1) * limit
                end_index = start_index + limit
                paginated_resumes = all_resumes[start_index:end_index]

                # Log activity
                log_activity('Viewed resumes', f'Page {page}, {len(paginated_resumes)} of {total_count} resumes from database', 'Admin')

                return jsonify({
                    'resumes': paginated_resumes,
                    'count': total_count,
                    'page': page,
                    'limit': limit,
                    'total_pages': (total_count + limit - 1) // limit,
                    'source': 'database'
                })
            else:
                # Fallback to mock data
                total_count = len(resumes_data)
                start_index = (page - 1) * limit
                end_index = start_index + limit
                paginated_resumes = resumes_data[start_index:end_index]

                return jsonify({
                    'resumes': paginated_resumes,
                    'count': total_count,
                    'page': page,
                    'limit': limit,
                    'total_pages': (total_count + limit - 1) // limit,
                    'source': 'fallback'
                })

        except Exception as e:
            # Log activity
            log_activity('Database error', f'Connection error: {str(e)}')

            # Apply pagination to fallback data
            total_count = len(resumes_data)
            start_index = (page - 1) * limit
            end_index = start_index + limit
            paginated_resumes = resumes_data[start_index:end_index]

            return jsonify({
                'resumes': paginated_resumes,
                'count': total_count,
                'page': page,
                'limit': limit,
                'total_pages': (total_count + limit - 1) // limit,
                'source': 'fallback',
                'error': f'Database connection error: {str(e)}'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes/<resume_id>', methods=['GET'])
def get_resume(resume_id):
    """Get a specific resume by ID"""
    try:
        resume = next((r for r in resumes_data if r['id'] == resume_id), None)

        if not resume:
            return jsonify({'error': 'Resume not found'}), 404

        return jsonify(resume)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/resumes/<resume_id>/download', methods=['GET'])
def download_resume_pdf(resume_id):
    """Download the original PDF file for a resume from MongoDB GridFS"""
    try:
        if not MONGODB_AVAILABLE or not mongo_client:
            return jsonify({'error': 'MongoDB not available'}), 503

        # Get the resume document to find the pdf_file_id
        try:
            from bson import ObjectId
            from helperMongoDb import COLLECTION_NAME

            print(f"🔍 DEBUG: Searching for resume with ID: {resume_id}")
            print(f"🔍 DEBUG: Using collection: {COLLECTION_NAME}")

            collection = mongo_client.db[COLLECTION_NAME]
            resume_doc = collection.find_one({'_id': ObjectId(resume_id)})

            if not resume_doc:
                print(f"❌ DEBUG: Resume not found with ID: {resume_id}")
                return jsonify({'error': 'Resume not found'}), 404

            print(f"✅ DEBUG: Found resume document")
            print(f"🔍 DEBUG: Resume doc keys: {list(resume_doc.keys())}")

            pdf_file_id = resume_doc.get('pdf_file_id')
            print(f"🔍 DEBUG: pdf_file_id from document: {pdf_file_id}")

            if not pdf_file_id:
                print(f"❌ DEBUG: No pdf_file_id found in resume document")
                return jsonify({'error': 'No PDF file associated with this resume'}), 404

            # Retrieve the PDF file from GridFS
            import gridfs
            from io import BytesIO

            fs = gridfs.GridFS(mongo_client.db)
            print(f"🔍 DEBUG: Attempting to retrieve file from GridFS with ID: {pdf_file_id}")

            try:
                # Check if file exists in GridFS first
                if not fs.exists(pdf_file_id):
                    print(f"❌ DEBUG: File does not exist in GridFS: {pdf_file_id}")
                    return jsonify({'error': 'PDF file not found in GridFS'}), 404

                grid_out = fs.get(pdf_file_id)
                print(f"✅ DEBUG: Successfully retrieved file from GridFS")
                print(f"🔍 DEBUG: File info - filename: {grid_out.filename}, length: {grid_out.length}")

                # Create a BytesIO object to hold the file data
                file_data = BytesIO()
                file_data.write(grid_out.read())
                file_data.seek(0)

                # Get the original filename or create a default one
                original_filename = resume_doc.get('original_filename', f'resume_{resume_id}.pdf')
                if not original_filename.lower().endswith('.pdf'):
                    original_filename += '.pdf'

                print(f"📁 DEBUG: Using filename: {original_filename}")

                # Log the download activity
                log_activity('PDF downloaded', f'Resume ID: {resume_id}, File: {original_filename}', 'Admin')

                # Return the file as a download
                from flask import send_file
                return send_file(
                    file_data,
                    as_attachment=True,
                    download_name=original_filename,
                    mimetype='application/pdf'
                )

            except gridfs.NoFile as e:
                print(f"❌ DEBUG: GridFS NoFile error: {e}")
                return jsonify({'error': 'PDF file not found in GridFS'}), 404
            except Exception as gridfs_error:
                print(f"❌ DEBUG: GridFS error: {gridfs_error}")
                return jsonify({'error': f'GridFS error: {str(gridfs_error)}'}), 500

        except Exception as e:
            print(f"❌ DEBUG: Error retrieving resume or PDF: {e}")
            import traceback
            print(f"❌ DEBUG: Full traceback: {traceback.format_exc()}")
            return jsonify({'error': f'Database error: {str(e)}'}), 500

    except Exception as e:
        print(f"❌ DEBUG: Download PDF error: {e}")
        import traceback
        print(f"❌ DEBUG: Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Download failed: {str(e)}'}), 500

@app.route('/api/resumes/<resume_id>', methods=['DELETE'])
def delete_resume(resume_id):
    """Delete a resume from MongoDB and remove associated files"""
    try:
        if MONGODB_AVAILABLE and mongo_client:
            # Try to delete from MongoDB first
            try:
                success = MongoDBClient.delete_data_with_id(resume_id)
                if success:
                    log_activity('Resume deleted from MongoDB', f'ID: {resume_id}', 'Admin')
                    return jsonify({'message': 'Resume deleted successfully from database'})
                else:
                    return jsonify({'error': 'Resume not found in database'}), 404
            except Exception as e:
                print(f"MongoDB delete error: {e}")
                return jsonify({'error': f'Database error: {str(e)}'}), 500
        else:
            # Fallback to mock data deletion
            global resumes_data
            resume = next((r for r in resumes_data if r['id'] == resume_id), None)

            if not resume:
                return jsonify({'error': 'Resume not found'}), 404

            # Remove from mock data
            resumes_data = [r for r in resumes_data if r['id'] != resume_id]

            # Delete file if exists
            if 'filepath' in resume and os.path.exists(resume['filepath']):
                os.remove(resume['filepath'])

            # Log activity
            log_activity('Resume deleted from mock data', resume.get('filename', resume_id), 'Admin')

            return jsonify({'message': 'Resume deleted successfully'})

    except Exception as e:
        print(f"Delete resume error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/activity', methods=['GET'])
def get_activity_log():
    """Get activity log"""
    try:
        return jsonify({
            'activities': activity_log,
            'count': len(activity_log)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """Get real dashboard statistics from MongoDB and activity log"""
    try:
        # Get real total resumes from MongoDB
        total_resumes = 0
        recent_uploads = 0

        if MONGODB_AVAILABLE and mongo_client:
            try:
                print(f"🔍 DEBUG Stats: MongoDB available, getting data...")
                print(f"🔍 DEBUG Stats: mongo_client.db.name: {mongo_client.db.name}")

                # Get all resumes from MongoDB using the same method as /showall
                cursorShowAll = mongo_client.get_all_resume_data()
                all_resumes = [doc for doc in cursorShowAll]
                total_resumes = len(all_resumes)

                print(f"🔍 DEBUG Stats: Retrieved {total_resumes} resumes from MongoDB")

                # Calculate recent uploads (last 2 days for more accurate "This Week" count)
                two_days_ago = datetime.now() - timedelta(days=2)
                recent_uploads = 0

                print(f"🔍 DEBUG Stats: Calculating recent uploads since {two_days_ago} (last 2 days)")

                for i, resume in enumerate(all_resumes):
                    try:
                        timestamp = resume.get('timestamp')
                        if timestamp:
                            # Handle different timestamp formats
                            if isinstance(timestamp, str):
                                # Remove 'Z' and handle ISO format
                                timestamp_str = timestamp.replace('Z', '+00:00')
                                resume_date = datetime.fromisoformat(timestamp_str)
                            else:
                                # Handle datetime objects
                                resume_date = timestamp

                            if resume_date >= two_days_ago:
                                recent_uploads += 1
                                if recent_uploads <= 10:  # Log first 10 recent uploads for debugging
                                    print(f"🔍 DEBUG Stats: Recent upload {recent_uploads}: {resume.get('original_filename', 'No filename')} at {resume_date}")
                    except Exception as ts_error:
                        print(f"⚠️ DEBUG Stats: Timestamp error for resume {i}: {ts_error}")
                        continue

                print(f"📊 Real stats - Total resumes: {total_resumes}, Recent uploads: {recent_uploads}")

            except Exception as mongo_error:
                print(f"⚠️ MongoDB stats error: {mongo_error}")
                # Fallback to sample data count
                total_resumes = len(resumes_data)
                recent_uploads = len([r for r in resumes_data if
                                    (datetime.now() - datetime.fromisoformat(r['uploaded_at'])).days < 7])
        else:
            # Fallback to sample data
            total_resumes = len(resumes_data)
            recent_uploads = len([r for r in resumes_data if
                                (datetime.now() - datetime.fromisoformat(r['uploaded_at'])).days < 7])

        # Get activity statistics from both memory and MongoDB
        total_searches = len([a for a in activity_log if 'Search completed' in a.get('action', '')])
        total_activities = len(activity_log)

        print(f"🔍 DEBUG: Memory activity count: {total_activities}")
        print(f"🔍 DEBUG: Memory search count: {total_searches}")
        print(f"🔍 DEBUG: Sample activities: {[a.get('action', 'No action') for a in activity_log[:5]]}")

        # If we have MongoDB, get more accurate counts
        if MONGODB_AVAILABLE and mongo_client:
            try:
                collection = mongo_client.db[ACTIVITY_COLLECTION]
                # Count total searches from MongoDB
                search_count = collection.count_documents({"action": {"$regex": "Search completed"}})
                if search_count > total_searches:
                    total_searches = search_count

                # Count total activities from MongoDB
                activity_count = collection.count_documents({})
                if activity_count > total_activities:
                    total_activities = activity_count

                print(f"📊 MongoDB activity stats - Searches: {search_count}, Activities: {activity_count}")
            except Exception as mongo_activity_error:
                print(f"⚠️ Error getting activity stats from MongoDB: {mongo_activity_error}")
                # Use memory counts as fallback

        stats = {
            'total_resumes': total_resumes,
            'recent_uploads': recent_uploads,
            'total_searches': total_searches,
            'total_activities': total_activities,
            'mongodb_available': MONGODB_AVAILABLE,
            'last_updated': datetime.now().isoformat()
        }

        print(f"📊 Returning stats: {stats}")
        return jsonify(stats)

    except Exception as e:
        print(f"❌ Stats endpoint error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/test-search', methods=['GET'])
def test_search_route():
    """Test route to verify search history routing works"""
    return jsonify({'message': 'Search history test route works!', 'status': 'success'})

@app.route('/api/search-history', methods=['GET'])
def get_search_history():
    """Get search history from MongoDB"""
    try:
        print(f"🔍 DEBUG: GET /api/search-history called")
        limit = int(request.args.get('limit', 50))
        search_history = load_search_history_from_db(limit)

        return jsonify({
            'history': search_history,
            'count': len(search_history)
        })
    except Exception as e:
        print(f"❌ Error getting search history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search-history', methods=['POST'])
def save_search_history():
    """Save search history item to MongoDB"""
    try:
        print(f"🔍 DEBUG: POST /api/search-history called")
        data = request.get_json()

        # Validate required fields
        if not data.get('query'):
            return jsonify({'error': 'Query is required'}), 400

        # Save to MongoDB
        item_id = save_search_history_to_db(data)

        if item_id:
            return jsonify({
                'message': 'Search history saved successfully',
                'id': item_id
            })
        else:
            return jsonify({'error': 'Failed to save search history'}), 500

    except Exception as e:
        print(f"❌ Error saving search history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search-history', methods=['DELETE'])
def clear_search_history():
    """Clear all search history from MongoDB"""
    try:
        print(f"🔍 DEBUG: DELETE /api/search-history called")
        success = delete_search_history_from_db()

        if success:
            return jsonify({'message': 'Search history cleared successfully'})
        else:
            return jsonify({'error': 'Failed to clear search history'}), 500

    except Exception as e:
        print(f"❌ Error clearing search history: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search-history/<item_id>', methods=['DELETE'])
def delete_search_history_item(item_id):
    """Delete specific search history item from MongoDB"""
    try:
        print(f"🔍 DEBUG: DELETE /api/search-history/{item_id} called")
        print(f"🔍 DEBUG: Item ID received: '{item_id}' (type: {type(item_id)}, length: {len(item_id)})")

        success = delete_search_history_from_db(item_id)

        if success:
            print(f"✅ DEBUG: Successfully deleted search history item: {item_id}")
            return jsonify({'message': 'Search history item deleted successfully'})
        else:
            print(f"❌ DEBUG: Failed to delete search history item: {item_id}")
            return jsonify({'error': 'Search history item not found'}), 404

    except Exception as e:
        print(f"❌ Error deleting search history item: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/search-history/<item_id>/results', methods=['GET'])
def get_search_history_results(item_id):
    """Get full search results for a specific search history item"""
    try:
        print(f"🔍 DEBUG: GET /api/search-history/{item_id}/results called")

        if MONGODB_AVAILABLE and mongo_client:
            collection = mongo_client.db[SEARCH_HISTORY_COLLECTION]

            # Try to find by custom 'id' field first
            search_item = collection.find_one({"id": item_id})

            if not search_item:
                # Try to find by MongoDB _id
                try:
                    from bson import ObjectId
                    search_item = collection.find_one({"_id": ObjectId(item_id)})
                except:
                    pass

            if not search_item:
                # Try as string _id
                search_item = collection.find_one({"_id": item_id})

            if search_item:
                results = search_item.get('results', [])
                print(f"📊 Found {len(results)} results for search history item: {item_id}")

                return jsonify({
                    'results': results,
                    'count': len(results),
                    'query': search_item.get('query', ''),
                    'timestamp': search_item.get('timestamp', '')
                })
            else:
                print(f"❌ Search history item not found: {item_id}")
                return jsonify({'error': 'Search history item not found'}), 404
        else:
            return jsonify({'error': 'MongoDB not available'}), 503

    except Exception as e:
        print(f"❌ Error getting search history results: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/export', methods=['POST'])
def export_resumes():
    """Export resumes data"""
    try:
        data = request.get_json()
        format_type = data.get('format', 'json')
        resume_ids = data.get('resume_ids', [])

        # Filter resumes if specific IDs provided
        if resume_ids:
            export_data = [r for r in resumes_data if r['id'] in resume_ids]
        else:
            export_data = resumes_data

        # Log activity
        log_activity('Data exported', f'{len(export_data)} resumes in {format_type} format', 'Admin')

        return jsonify({
            'data': export_data,
            'format': format_type,
            'count': len(export_data),
            'exported_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Notification endpoints
@app.route('/api/notifications', methods=['GET'])
def get_notifications():
    """Get all notifications from MongoDB"""
    try:
        print(f"🔔 DEBUG: GET /api/notifications called")
        limit = int(request.args.get('limit', 50))
        notifications = load_notifications_from_db(limit)
        unread_count = sum(1 for n in notifications if n.get('unread', False))

        return jsonify({
            'notifications': notifications,
            'count': len(notifications),
            'unread_count': unread_count
        })
    except Exception as e:
        print(f"❌ Error getting notifications: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/<notification_id>/read', methods=['PUT'])
def mark_notification_as_read(notification_id):
    """Mark a specific notification as read"""
    try:
        print(f"🔔 DEBUG: PUT /api/notifications/{notification_id}/read called")
        success = mark_notification_read(notification_id)

        if success:
            return jsonify({'message': 'Notification marked as read'})
        else:
            return jsonify({'error': 'Notification not found'}), 404

    except Exception as e:
        print(f"❌ Error marking notification as read: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/mark-all-read', methods=['PUT'])
def mark_all_notifications_as_read():
    """Mark all notifications as read"""
    try:
        print(f"🔔 DEBUG: PUT /api/notifications/mark-all-read called")
        count = mark_all_notifications_read()

        return jsonify({
            'message': f'Marked {count} notifications as read',
            'count': count
        })

    except Exception as e:
        print(f"❌ Error marking all notifications as read: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/<notification_id>', methods=['DELETE'])
def delete_notification(notification_id):
    """Delete a specific notification"""
    try:
        print(f"🔔 DEBUG: DELETE /api/notifications/{notification_id} called")
        success = delete_notification_from_db(notification_id)

        if success:
            return jsonify({'message': 'Notification deleted successfully'})
        else:
            return jsonify({'error': 'Notification not found'}), 404

    except Exception as e:
        print(f"❌ Error deleting notification: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications', methods=['DELETE'])
def clear_notifications():
    """Clear all notifications"""
    try:
        print(f"🔔 DEBUG: DELETE /api/notifications called")
        count = clear_all_notifications()

        return jsonify({
            'message': f'Cleared {count} notifications',
            'count': count
        })

    except Exception as e:
        print(f"❌ Error clearing notifications: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Add some sample data for demonstration
    sample_resumes = [
        {
            'id': str(uuid.uuid4()),
            'filename': 'john_doe_resume.pdf',
            'filepath': './sample/john_doe_resume.pdf',
            'name': 'John Doe',
            'title': 'Senior Software Engineer',
            'email': '<EMAIL>',
            'phone': '+****************',
            'location': 'San Francisco, CA',
            'experience': '5+ years',
            'skills': ['Python', 'React', 'Node.js', 'AWS'],
            'education': 'MS Computer Science',
            'summary': 'Experienced software engineer with expertise in full-stack development...',
            'uploaded_at': datetime.now().isoformat()
        },
        {
            'id': str(uuid.uuid4()),
            'filename': 'jane_smith_cv.pdf',
            'filepath': './sample/jane_smith_cv.pdf',
            'name': 'Jane Smith',
            'title': 'Frontend Developer',
            'email': '<EMAIL>',
            'phone': '+****************',
            'location': 'New York, NY',
            'experience': '3+ years',
            'skills': ['React', 'TypeScript', 'CSS', 'JavaScript'],
            'education': 'BS Computer Science',
            'summary': 'Creative frontend developer passionate about user experience...',
            'uploaded_at': datetime.now().isoformat()
        }
    ]

    resumes_data.extend(sample_resumes)

    # Load existing activity log from MongoDB
    print("📊 Loading activity log from MongoDB...")
    if load_activity_log_from_db():
        print(f"✅ Loaded {len(activity_log)} existing activities from MongoDB")
    else:
        print("⚠️ Could not load activity log from MongoDB, starting fresh")

    # Add startup activities
    log_activity('System started', 'Resume AI Agent backend initialized', 'System')
    log_activity('Sample data loaded', f'{len(sample_resumes)} sample resumes', 'System')

    from helperMongoDb import DATABASE_NAME, COLLECTION_NAME

    print("🚀 Resume AI Agent Backend API starting...")
    print(f"📊 MongoDB connected to: {DATABASE_NAME}.{COLLECTION_NAME}")
    print("📊 Dashboard: http://************:3000")
    print("🔗 API Base URL: http://************:8002/api")
    print("📋 Health Check: http://************:8002/health")
    print("📋 Show All Resumes: http://************:8002/showall")
    print("📋 Query Endpoint: http://************:8002/query/")
    print("📋 Search History: http://************:8002/api/search-history")

    # Debug: Print all registered routes
    print("\n🔍 Registered Flask routes:")
    for rule in app.url_map.iter_rules():
        print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")

    # Configure Flask with basic settings
    debug_mode = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    app.run(debug=debug_mode, host='0.0.0.0', port=8002)
