{"Resume": {"PersonalInformation": {"FullName": "VIPUL SOLANKI", "Gender": "", "BirthDate": "", "Email": "<EMAIL>", "Address": "", "ContactNumber": "9672707996", "LinkedInProfile": "www.linkedin.com/in/vipul-991a271a6"}, "Objective": "Aspiring Data Analyst looking for opportunities. A team player and a highly\nmotivated individual willing to learn new technologies and methods related to data\nanalysis. Able to adapt to different work culture and always ready to accept new\nchallenges.", "Education": [{"Degree": "Bachelor of Technology in Mechanical\nEngineering", "Institution": "Poornima College of Engineering, RTU", "GraduationYear": "2021", "GPA/Marks/%": ""}, {"Degree": "Diploma in Mechanical Engineering", "Institution": "Govt. Polytechnic College Kota, BTER", "GraduationYear": "2017", "GPA/Marks/%": ""}], "WorkExperience": [{"CompanyName": "Grras Solutions pvt. ltd.", "Role": "Data Analyst trainee", "StartYear": "07/2023", "EndYear": "'12/2023'", "Description/Responsibility": "Worked on Real time projects by sql, powerbi and excel"}, {"CompanyName": "DODSAL ENTERPRISES PVT. LTD", "Role": "NPCIL", "StartYear": "11/2022", "EndYear": "'03/2023'", "Description/Responsibility": "Quality check and Quality assurance (mechanical)"}, {"CompanyName": "Ultimate Inspections", "Role": "NPCIL", "StartYear": "10/2021", "EndYear": "'10/2022'", "Description/Responsibility": "Pre Service Inspection of Components Installed in Reactor\nBuilding"}], "Skills": ["SQL | PowerBI | MS Excel |\nGoogle Sheets | MS Power Point"], "Certifications": [{"CertificationName": "Data Analyst Training Program", "IssuingOrganization": "Grras\nSolutions Pvt. Ltd.", "IssueDate": "2023", "ExpiryDate": "'None'"}, {"CertificationName": "SQL Basic", "IssuingOrganization": "HackerRank", "IssueDate": "2023", "ExpiryDate": "'None'"}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [""], "Projects": [{"ProjectName": "Hotel revenue generation\nanalysis", "Description": "Data transformation / cleaning -noisy\ndata reduction, removed duplicates,\n conditional formatting,\n Data modeling - relationship\ndevelopment between fact table and\n dimensions tables by using star\n schema\nDAX function - revenue analysis per\navailable/sellable rooms\n Travel booking platforms analysis\n Tier-1, Tier-2 class analysis\n  dependencies to weekends and\n weekdays\n Data visualization - created\ndashboard for making informed\n decisions , helped in revenue\ngeneration analysis\nKPI generation - created various key\npointers as percentage variation\nanalysis.", "TechnologiesUsed": ["Microsoft Power Bi\nMicrosoft Excel "], "Role": ""}, {"ProjectName": "DVD Rental Store Analysis", "Description": "Generated KPIs to take informed\ndecision for business - Customer\nanalysis , Sales analysis ,Film\n analysis, Actors analysis.\n.", "TechnologiesUsed": ["PostgreSQL (Pg admin)\nMicrosoft Excel "], "Role": ""}]}}