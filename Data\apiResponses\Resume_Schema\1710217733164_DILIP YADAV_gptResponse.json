{"id": "chatcmpl-BeLW4aF6tkfmxVxKW3bcyYwZ1Wb8H", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"DILIP YADAV\",\"Gender\":\"Male\",\"BirthDate\":19960107,\"Email\":\"<EMAIL>\",\"Address\":\"Shramik Colony Rau.\",\"ContactNumber\":\"9630386274\",\"LinkedInProfile\":\"\"},\"Objective\":\"Intend to build a career with leading institute of hi-tech environment with committed & dedicated people, which will help me to explore myself fully and realize my potential.\",\"Education\":[{\"Degree\":\"B.Ped\",\"Institution\":\"Eklavya Institute\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"B.Com\",\"Institution\":\"Khalsa College\",\"GraduationYear\":\"2016\",\"GPA/Marks/%\":53},{\"Degree\":\"Higher Secondary\",\"Institution\":\"Vaisnav Higher Secondary School\",\"GraduationYear\":\"2013\",\"GPA/Marks/%\":57},{\"Degree\":\"High School\",\"Institution\":\"<PERSON><PERSON>\",\"GraduationYear\":\"2011\",\"GPA/Marks/%\":64}],\"WorkExperience\":[{\"CompanyName\":\"South Valley International School\",\"Role\":\"Sports Teacher(Cricket)\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Discipline Incharge\"},{\"CompanyName\":\"Vedanta Global School\",\"Role\":\"Sports Teacher(Cricket)\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Discipline Incharge, Sports other event Coordinator ,etc\"},{\"CompanyName\":\"M. B. Khalsa College\",\"Role\":\"Sports Teacher(Cricket)\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"0\"},{\"CompanyName\":\"Star Cricket Club\",\"Role\":\"Cricket Coach\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"0\"}],\"TotalWorkExperienceInYears\":10,\"Skills\":[\"Windows-95/98/2000/XP/VISTA/7\",\"MS-Office\",\"Zoom\",\"Google Meet\",\"Google Classroom\",\"Google forms\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[\"English\",\"Hindi\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "annotations": []}}], "created": 1748955528, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_a288987b44", "usage": {"completion_tokens": 417, "prompt_tokens": 2390, "total_tokens": 2807, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}}}