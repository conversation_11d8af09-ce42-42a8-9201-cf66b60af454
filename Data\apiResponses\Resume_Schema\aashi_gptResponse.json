{"id": "chatcmpl-B4kDx3TKsRluZmX6ukycoKU5PA7vR", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"ASTHA SHUKLA\",\"Gender\":\"Female\",\"BirthDate\":19971201,\"Email\":\"<EMAIL>\",\"Address\":\"Ward no 07, In Front of Ram Mandir, Naya <PERSON>, Banda, Sagar,(M.P.) 470335.\",\"ContactNumber\":\"8358046320\",\"LinkedInProfile\":\"\"},\"Objective\":\"Seeking an opportunity to serve as a Teacher for a reputed group. D.ed and Masters in History. Skilled in Classroom Management and Lesson Planning. Capable of building an open and interactive environment to help students express themselves in a better way. Proficient in a range of teaching styles and communication...\",\"Education\":[{\"Degree\":\"D.El.Ed.\",\"Institution\":\"M.P. Board Bhopal\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"M.A. History\",\"Institution\":\"Maharaja Chhatrasal Bundelkhand University, Chhatarpur\",\"GraduationYear\":\"2020\",\"GPA/Marks/%\":0},{\"Degree\":\"B.Sc Computer Science\",\"Institution\":\"Dr. <PERSON> University Sagar\",\"GraduationYear\":\"2018\",\"GPA/Marks/%\":0},{\"Degree\":\"Intermediate\",\"Institution\":\"Govt Girls H.S. School Banda\",\"GraduationYear\":\"2014\",\"GPA/Marks/%\":73.4},{\"Degree\":\"10th class\",\"Institution\":\"Saraswati Sishu Mandir Banda\",\"GraduationYear\":\"2012\",\"GPA/Marks/%\":87.5}],\"WorkExperience\":[{\"CompanyName\":\"Saraswati Vidhya Mandir High School Banda\",\"Role\":\"Teacher\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"2 Years Teaching experience\"}],\"TotalWorkExperienceInYears\":2,\"Skills\":[\"Good Knowledge of Computer\",\"Good knowledge of data analysis\",\"Knowledge in preventive and corrective maintenance\",\"Hindi & English Typing\",\"Ability to Communicate with others\",\"Power Point\",\"MS Excel\",\"MS Word\",\"Good Knowledge of Internet\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[\"English\",\"Hindi\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": 1740470697, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_f9f4fb6dbf", "usage": {"completion_tokens": 435, "prompt_tokens": 3436, "total_tokens": 3871, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 3328}}}