{"id": "chatcmpl-B4kDxYSJlRqkv7q0qsJ7XC0Euxi4X", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"<PERSON><PERSON>\",\"Gender\": \"Female\",\"BirthDate\":19991216,\"Email\":\"<EMAIL>\",\"Address\":\"104/C, Trishala Apartment, Biyabani, Indore\",\"ContactNumber\":\"8269015590\",\"LinkedInProfile\":\"\"},\"Objective\":\"To use my potential to the fullest for the growth of the student community.\",\"Education\":[{\"Degree\":\"Bachelor of Science in C.S.\",\"Institution\":\"Vikram University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Master of Arts (English Lit.)\",\"Institution\":\"<PERSON><PERSON><PERSON> Vedic University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0},{\"Degree\":\"Bachelor of Education\",\"Institution\":\"Vikram University\",\"GraduationYear\":\"\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"Daffodils Smart School (CBSE), Mandsaur\",\"Role\":\"PGT English / Academic Coordinator\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Responsible for - Effective teaching of English (IX-XII), Framing School Annual Calendar, Planning and execution of various co-curricular, cultural and literary events, Managing assessments and creating blue print for the same, Maintaining Parent-Teacher coordination, Planning Remedial curriculum and strategy, Assessing books, notebooks etc of students, Managing Assembly, Creating spoken English environment, Anchoring in school events, Being updated according to NEP 2020, such as planning bagless days for vocational skills training, execution of hands-on-learning method, Art integrated teaching-learning process, Maintenance of weekly Teachers Diary by focusing on LOC, based on Bloom's Taxonomy, Development of 21st century skills of students, Experienced in invigilation and evaluation of CBSE board examinations.\"}],\"TotalWorkExperienceInYears\":4,\"Skills\":[\"Computer Proficient\",\"State level basketball player\",\"Trained guitarist\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": 1740470697, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_f9f4fb6dbf", "usage": {"completion_tokens": 417, "prompt_tokens": 2741, "total_tokens": 3158, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 2688}}}