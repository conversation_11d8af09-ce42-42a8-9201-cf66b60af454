{"id": "chatcmpl-B4P4ZHTqeySZOSzc6QwD941plSWtR", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"<PERSON>ka<PERSON>\",\"Gender\":\"Male\",\"BirthDate\":19900101,\"Email\":\"<EMAIL>\",\"Address\":\"201 Bajaj G Heritage 2, <PERSON><PERSON><PERSON>, Rajendra Nagar, Indore, Madhya Pradesh, 452012\",\"ContactNumber\":\"+91-9958464823\",\"LinkedInProfile\":\"https://www.linkedin.com/in/pankajgola\"},\"Objective\":\"Experienced for 3+ years. Expert in increasing test scores and student motivation. Driven by helping children to increase their knowledge and willing to unlock their potential.\",\"Education\":[{\"Degree\":\"Bachelor of Technology in Software Engineering\",\"Institution\":\"Delhi Technological University (DTU)\",\"GraduationYear\":\"2018\",\"GPA/Marks/%\":8.5},{\"Degree\":\"Bachelor of Education (Pursuing)\",\"Institution\":\"Gurugram University\",\"GraduationYear\":\"2024\",\"GPA/Marks/%\":8.0},{\"Degree\":\"Masters of Arts in English Literature (Pursuing)\",\"Institution\":\"Mahatma Gandhi Chitrakoot Gramodaya Vishwavidyalaya\",\"GraduationYear\":\"2025\",\"GPA/Marks/%\":8.2}],\"WorkExperience\":[{\"CompanyName\":\"Prakash Model School, Delhi\",\"Role\":\"Teacher for Science and Social Studies\",\"StartYear\":201901,\"EndYear\":202304,\"Description/Responsibility\":\"Taught Science and Social Studies for classes 8th, 9th, and 10th. Created weekly study plans, handled student queries, set test papers for weekly and sessional exams, and managed student skills to improve them.\"}],\"TotalWorkExperienceInYears\":4,\"Skills\":[\"Verbal communication\",\"Detail oriented\",\"Patience\",\"Parent communication\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[\"English\",\"Hindi\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": 1740389391, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_eb9dce56a8", "usage": {"completion_tokens": 387, "prompt_tokens": 2577, "total_tokens": 2964, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}}}