import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle, FiSettings, FiEye, FiAlertTriangle } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { API_ENDPOINTS } from '../config/api';
import ResumeModal from '../components/ResumeModal';
import './UploadResume.css';

const UploadResume = () => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [useAdvancedProcessor, setUseAdvancedProcessor] = useState(true);
  const [workers, setWorkers] = useState(4);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedResume, setSelectedResume] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
          } else if (error.code === 'file-invalid-type') {
            const supportedFormats = useAdvancedProcessor
              ? 'PDF, DOC, DOCX, JPG, PNG, BMP, TIFF, GIF'
              : 'PDF only';
            toast.error(`File ${file.name} is not supported. Supported formats: ${supportedFormats}`);
          }
        });
      });
    }

    // Handle accepted files
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 11),
      file,
      status: 'pending', // pending, uploading, success, error, skipped, duplicate
      progress: 0,
      error: null,
      message: null,
      resumeId: null, // Store resume ID for successful uploads
      resumeData: null // Store resume data for viewing
    }));

    setFiles(prev => [...prev, ...newFiles]);
  }, [useAdvancedProcessor]);

  // Define accepted file types based on processor mode
  const getAcceptedFileTypes = () => {
    if (useAdvancedProcessor) {
      return {
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/bmp': ['.bmp'],
        'image/tiff': ['.tiff'],
        'image/gif': ['.gif']
      };
    } else {
      return {
        'application/pdf': ['.pdf']
      };
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedFileTypes(),
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  });

  const removeFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast.warning('Please select files to upload');
      return;
    }

    setUploading(true);

    try {
      // Prepare FormData with all files
      const formData = new FormData();
      const pendingFiles = files.filter(f => f.status === 'pending');

      pendingFiles.forEach(fileItem => {
        formData.append('files', fileItem.file);
      });

      // Add processing options for advanced processor
      if (useAdvancedProcessor) {
        formData.append('workers', workers.toString());
      }

      // Update all files to uploading status
      setFiles(prev => prev.map(f =>
        f.status === 'pending'
          ? { ...f, status: 'uploading', progress: 0 }
          : f
      ));

      // Simulate progress for UI feedback
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f =>
          f.status === 'uploading' && f.progress < 90
            ? { ...f, progress: f.progress + 10 }
            : f
        ));
      }, 500); // Slower progress for advanced processing

      // Choose endpoint based on processor mode
      const endpoint = useAdvancedProcessor
        ? 'http://************:8002/api/upload-advanced'
        : API_ENDPOINTS.UPLOAD;

      console.log(`🚀 Using ${useAdvancedProcessor ? 'advanced' : 'legacy'} processor`);
      console.log(`📡 Endpoint: ${endpoint}`);

      // Make actual API call
      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.detail || 'Upload failed');
      }

      const result = await response.json();
      console.log('Upload result:', result);

      // Process the response and update individual file statuses
      await updateFileStatuses(result);

      // Show summary message based on processor type
      if (useAdvancedProcessor && result.processing_result) {
        const { processed_successfully, failed_files, duplicate_files } = result.processing_result;
        toast.success(`Advanced processing completed! Success: ${processed_successfully}, Failed: ${failed_files}, Duplicates: ${duplicate_files}`);

        if (result.invalid_files && result.invalid_files.length > 0) {
          toast.warning(`${result.invalid_files.length} file(s) had invalid formats`);
        }
      } else {
        toast.success(`Successfully processed ${result.processed_files || result.uploaded_files} file(s)!`);

        if (result.duplicate_files > 0) {
          toast.warning(`${result.duplicate_files} duplicate file(s) were skipped`);
        }
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Update all uploading files to error status
      setFiles(prev => prev.map(f =>
        f.status === 'uploading'
          ? { ...f, status: 'error', error: error.message }
          : f
      ));

      toast.error(`Upload failed: ${error.message}`);
    }

    setUploading(false);
  };

  const updateFileStatuses = async (result) => {
    try {
      if (useAdvancedProcessor && result.processing_result) {
        // Handle advanced processor response
        const { processed_files, failed_files_list, duplicate_files_list } = result.processing_result;

        setFiles(prev => prev.map(f => {
          if (f.status !== 'uploading') return f;

          const fileName = f.file.name;

          if (duplicate_files_list && duplicate_files_list.includes(fileName)) {
            return {
              ...f,
              status: 'skipped',
              progress: 100,
              message: 'Resume already exists in database'
            };
          } else if (failed_files_list && failed_files_list.includes(fileName)) {
            return {
              ...f,
              status: 'error',
              progress: 100,
              error: 'Processing failed, please try again later'
            };
          } else if (processed_files && processed_files.includes(fileName)) {
            return {
              ...f,
              status: 'success',
              progress: 100,
              message: 'Successfully processed and stored'
            };
          } else {
            // Default to success if file was uploaded but not in specific lists
            return {
              ...f,
              status: 'success',
              progress: 100,
              message: 'Successfully processed and stored'
            };
          }
        }));
      } else {
        // Handle legacy processor response
        const duplicates = result.duplicates || [];
        const savedFiles = result.saved_files || [];

        setFiles(prev => prev.map(f => {
          if (f.status !== 'uploading') return f;

          const fileName = f.file.name;

          if (duplicates.includes(fileName)) {
            return {
              ...f,
              status: 'skipped',
              progress: 100,
              message: 'Resume already exists in database'
            };
          } else if (savedFiles.includes(fileName)) {
            return {
              ...f,
              status: 'success',
              progress: 100,
              message: 'Successfully processed and stored'
            };
          } else {
            return {
              ...f,
              status: 'error',
              progress: 100,
              error: 'Processing failed, please try again later'
            };
          }
        }));
      }

      // Fetch updated resume data for successful uploads to enable viewing
      setTimeout(() => {
        fetchResumeDataForSuccessfulUploads();
      }, 2000); // Wait a bit for backend processing to complete

    } catch (error) {
      console.error('Error updating file statuses:', error);
    }
  };

  const transformResumeData = (mongoResume) => {
    try {
      const resumeData = mongoResume.Resume || {};
      const personalInfo = resumeData.PersonalInformation || {};
      const contactInfo = resumeData.ContactInformation || {};

      // Extract skills
      const skills = resumeData.Skills || [];

      // Extract work experience for display
      const workExp = resumeData.WorkExperience || [];
      const experienceDisplay = workExp.length > 0
        ? `${workExp.length} position${workExp.length > 1 ? 's' : ''}`
        : 'No experience listed';

      // Extract education for display
      const education = resumeData.Education || [];
      const educationDisplay = education.length > 0
        ? `${education.length} qualification${education.length > 1 ? 's' : ''}`
        : 'No education listed';

      // Format address
      const address = personalInfo.Address || contactInfo.Address || 'Not Mentioned';

      // Format title/position
      const title = workExp.length > 0
        ? (workExp[0].Role || workExp[0].JobTitle || workExp[0].Position || 'Professional')
        : 'Professional';

      // Format added date
      const addedDate = mongoResume.timestamp
        ? new Date(mongoResume.timestamp).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })
        : 'Recently added';

      return {
        id: mongoResume._id || Math.random().toString(36).substring(2, 11),
        name: personalInfo.FullName || personalInfo.Name || 'Unknown',
        title: title,
        location: address,
        email: personalInfo.Email || contactInfo.Email || 'Not Mentioned',
        phone: personalInfo.ContactNumber || personalInfo.Phone || contactInfo.Phone || 'Not Mentioned',
        skills: skills,
        summary: resumeData.Summary || resumeData.Objective || 'No summary available',
        score: Math.floor(Math.random() * 30) + 70, // Random score between 70-100 for demo
        experience: workExp.length > 0
          ? workExp.map(exp => `${exp.Role || exp.JobTitle || 'Position'} at ${exp.CompanyName || 'Company'}`).join(', ')
          : 'No work experience listed',
        education: education.length > 0
          ? education.map(edu => `${edu.Degree || 'Degree'} from ${edu.Institution || 'Institution'}`).join(', ')
          : 'No education information available',
        experienceDisplay: experienceDisplay,
        educationDisplay: educationDisplay,
        addedDate: addedDate,
        rawData: mongoResume,
        fullData: resumeData,
        originalData: mongoResume
      };
    } catch (error) {
      console.error('Error transforming resume data:', error);
      return null;
    }
  };

  const fetchResumeDataForSuccessfulUploads = async () => {
    try {
      console.log('🔄 Fetching resume data for successful uploads...');

      // Get all resumes from backend to find newly uploaded ones
      const response = await fetch('http://************:8002/showall');
      if (response.ok) {
        const allResumes = await response.json();
        console.log('📊 Fetched resumes from backend:', allResumes.length);

        // Update files with resume data for successful uploads
        setFiles(prev => prev.map(f => {
          if (f.status === 'success' && !f.resumeData) {
            console.log(`🔍 Looking for resume data for file: ${f.file.name}`);

            // Find matching resume by filename (try multiple matching strategies)
            const matchingResume = allResumes.find(resume => {
              // Strategy 1: Exact filename match
              if (resume.original_filename === f.file.name) {
                return true;
              }

              // Strategy 2: Filename without extension match
              const fileNameWithoutExt = f.file.name.replace(/\.[^/.]+$/, "");
              const resumeNameWithoutExt = (resume.original_filename || '').replace(/\.[^/.]+$/, "");
              if (resumeNameWithoutExt === fileNameWithoutExt) {
                return true;
              }

              // Strategy 3: Check if uploaded recently (within last 5 minutes)
              if (resume.timestamp) {
                const resumeTime = new Date(resume.timestamp);
                const now = new Date();
                const timeDiff = (now - resumeTime) / (1000 * 60); // difference in minutes
                if (timeDiff <= 5) {
                  return true;
                }
              }

              return false;
            });

            if (matchingResume) {
              console.log(`✅ Found matching resume for ${f.file.name}:`, matchingResume._id);

              // Transform the resume data to the expected format
              const transformedResume = transformResumeData(matchingResume);

              if (transformedResume) {
                console.log(`🔄 Transformed resume data for ${f.file.name}:`, transformedResume);
                return {
                  ...f,
                  resumeId: matchingResume._id,
                  resumeData: transformedResume
                };
              } else {
                console.log(`❌ Failed to transform resume data for ${f.file.name}`);
              }
            } else {
              console.log(`❌ No matching resume found for ${f.file.name}`);
            }
          }
          return f;
        }));
      }
    } catch (error) {
      console.error('Error fetching resume data:', error);
    }
  };

  const handleViewResume = (fileItem) => {
    console.log('🔍 Viewing resume for file:', fileItem.file.name);
    console.log('📄 File item data:', fileItem);
    console.log('📋 Resume data:', fileItem.resumeData);

    if (fileItem.resumeData) {
      console.log('✅ Setting selected resume:', fileItem.resumeData);
      setSelectedResume(fileItem.resumeData);
      setIsModalOpen(true);
    } else {
      console.log('❌ No resume data available for file:', fileItem.file.name);
      toast.warning('Resume data not available yet. Please wait a moment and try again.');

      // Try to fetch resume data again
      fetchResumeDataForSuccessfulUploads();
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedResume(null);
  };

  const clearAll = () => {
    setFiles([]);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    // Round to whole number, no decimal points
    return Math.round(bytes / Math.pow(k, i)) + ' ' + sizes[i];
  };



  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <FiCheck className="status-icon success" />;
      case 'error':
        return <FiAlertCircle className="status-icon error" />;
      case 'skipped':
      case 'duplicate':
        return <FiAlertTriangle className="status-icon warning" />;
      case 'uploading':
        return <div className="loading-spinner" />;
      default:
        return <FiFile className="status-icon pending" />;
    }
  };

  return (
    <div className="upload-page">
      <div className="page-header">
        <div className="header-content">
          <div className="header-text">
            <h1>Upload Resume</h1>
          </div>
          <button
            className="settings-btn"
            onClick={() => setShowSettings(!showSettings)}
            title="Processing Settings"
          >
            <FiSettings size={20} />
          </button>
        </div>
      </div>

      {showSettings && (
        <div className="settings-panel">
          <h3>Processing Settings</h3>
          <div className="setting-group">
            <label className="setting-label">
              <input
                type="checkbox"
                checked={useAdvancedProcessor}
                onChange={(e) => setUseAdvancedProcessor(e.target.checked)}
              />
              <span className="checkmark"></span>
              Use Advanced Processor
            </label>
            <p className="setting-description">
              {useAdvancedProcessor
                ? 'Supports multiple formats (PDF, DOC, DOCX, Images) with parallel processing'
                : 'PDF-only processing with legacy pipeline'
              }
            </p>
          </div>

          {useAdvancedProcessor && (
            <div className="setting-group">
              <label className="setting-label">
                Processing Workers: {workers}
              </label>
              <input
                type="range"
                min="1"
                max="8"
                value={workers}
                onChange={(e) => setWorkers(parseInt(e.target.value))}
                className="worker-slider"
              />
              <p className="setting-description">
                More workers = faster processing for multiple files
              </p>
            </div>
          )}
        </div>
      )}

      <div className="upload-container">
        {/* Upload Area */}
        <div className="upload-section">
          <div
            {...getRootProps()}
            className={`dropzone ${isDragActive ? 'active' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="dropzone-content">
              <div className="upload-icon">
                <FiUpload size={48} />
              </div>
              <h3>Click or drag to upload your resume</h3>
              <p>
                Supported formats: {useAdvancedProcessor
                  ? 'PDF, DOC, DOCX, JPG, PNG, BMP, TIFF, GIF'
                  : 'PDF only'
                }
              </p>
              <div className="upload-specs">
                <div className="spec-item">
                  <span>📄 Maximum file size: 10MB</span>
                </div>
                <div className="spec-item">
                  <span>📁 Multiple files supported</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          {files.length > 0 && (
            <div className="upload-actions">
              <button 
                className="btn btn-secondary"
                onClick={clearAll}
                disabled={uploading}
              >
                Clear All
              </button>
              <button 
                className="btn btn-primary"
                onClick={uploadFiles}
                disabled={uploading || files.every(f => f.status === 'success')}
              >
                {uploading ? 'Processing...' : 'Extract Data'}
              </button>
            </div>
          )}
        </div>

        {/* File List */}
        {files.length > 0 && (
          <div className="file-list-section">
            <div className="section-header">
              <h3>Selected Files ({files.length})</h3>
            </div>
            
            <div className="file-list">
              {files.map((fileItem) => (
                <div key={fileItem.id} className={`file-item ${fileItem.status}`}>
                  <div className="file-info">
                    <div className="file-icon">
                      {getStatusIcon(fileItem.status)}
                    </div>
                    <div className="file-details">
                      <div className="file-name">{fileItem.file.name}</div>
                      <div className="file-meta">
                        <span>{formatFileSize(fileItem.file.size)}</span>
                        {fileItem.file.type === 'application/pdf' && (
                          <>
                            <span>•</span>
                            <span>{Math.max(1, Math.round(fileItem.file.size / (75 * 1024)))} pages</span>
                          </>
                        )}
                        {fileItem.status === 'error' && fileItem.error && (
                          <>
                            <span>•</span>
                            <span className="error-text">{fileItem.error}</span>
                          </>
                        )}
                        {fileItem.status === 'skipped' && fileItem.message && (
                          <>
                            <span>•</span>
                            <span className="warning-text">{fileItem.message}</span>
                          </>
                        )}
                        {fileItem.status === 'success' && fileItem.message && (
                          <>
                            <span>•</span>
                            <span className="success-text">{fileItem.message}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="file-actions">
                    {fileItem.status === 'uploading' && (
                      <div className="progress-container">
                        <div className="progress-bar">
                          <div
                            className="progress-fill"
                            style={{ width: `${fileItem.progress}%` }}
                          />
                        </div>
                        <span className="progress-text">{fileItem.progress}%</span>
                      </div>
                    )}

                    {fileItem.status === 'success' && (
                      <div className="success-actions">
                        <button
                          className="view-btn"
                          onClick={() => handleViewResume(fileItem)}
                          title="View resume"
                          disabled={!fileItem.resumeData}
                        >
                          <FiEye size={16} />
                          View
                        </button>
                        <button
                          className="remove-btn"
                          onClick={() => removeFile(fileItem.id)}
                          title="Remove file"
                        >
                          <FiX size={16} />
                        </button>
                      </div>
                    )}

                    {fileItem.status !== 'uploading' && fileItem.status !== 'success' && (
                      <button
                        className="remove-btn"
                        onClick={() => removeFile(fileItem.id)}
                        title="Remove file"
                      >
                        <FiX size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Description Section */}
      <div className="description-section">
        <p>
          Upload resume files in multiple formats to extract information and add them to the Database collection.
          Our AI-powered system will automatically analyze and structure the data.
        </p>
      </div>

      {/* Resume Modal */}
      <ResumeModal
        resume={selectedResume}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onDelete={() => {}} // No delete functionality needed in upload page
      />
    </div>
  );
};

export default UploadResume;
