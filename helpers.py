import logging
import os
import mysql.connector
import json

from config import DB_CONFIG

# Configure Logging
strLogFile = "file_reader.log"
logging.basicConfig(filename=strLogFile, level=logging.INFO)
logger = logging.getLogger(__name__)


def connectToDatabase():
    """
    Establish a database connection using MySQL.

    Returns:
        mysql.connector.connection.MySQLConnection: Active database connection.
    """
    return mysql.connector.connect(**DB_CONFIG)


def ExecuteQuery(strSqlQuery: str):
    """
    Execute a SQL query securely and return the fetched results.

    Args:
        strSqlQuery (str): The SQL query string to execute.

    Returns:
        list: A list of rows (dict) returned by the query.
    """
    dbConnection = LoadJsonFile()
    try:
        dbCursor = dbConnection.cursor(dictionary=True)
        dbCursor.execute(strSqlQuery)
        listResults = dbCursor.fetchall()
        dbCursor.close()
        return listResults
    except mysql.connector.Error as dbError:
        logger.error(f"Database query error: {dbError}")
        raise
    finally:
        dbConnection.close()


def ReadTxtFile(strFilePath: str, strEncoding: str = "utf-8") -> str:
    """
    Read the entire content of a TXT file as a single string.

    Args:
        strFilePath (str): Path to the text file.
        strEncoding (str): Encoding format (default: utf-8).

    Returns:
        str: The entire content of the file.

    Raises:
        FileNotFoundError: If the file does not exist.
        PermissionError: If the file cannot be accessed.
        UnicodeDecodeError: If the file has invalid encoding.
    """
    if not os.path.exists(strFilePath):
        logger.error(f"File not found: {strFilePath}")
        raise FileNotFoundError(f"Error: The file '{strFilePath}' does not exist.")

    try:
        with open(strFilePath, "r", encoding=strEncoding, errors="replace") as fileObj:
            return fileObj.read()
    except PermissionError:
        logger.error(f"Permission denied: {strFilePath}")
        raise PermissionError(f"Error: Cannot access '{strFilePath}'. Check file permissions.")
    except UnicodeDecodeError:
        logger.error(f"Encoding issue with file: {strFilePath}")
        raise UnicodeDecodeError(f"Error: Unable to read '{strFilePath}' due to encoding issues.")


def LoadJsonFile(strFilePath: str, strEncoding: str = "utf-8"):
    """
    Load a JSON file and return its content as a dictionary.

    Args:
        strFilePath (str): Path to the JSON file.
        strEncoding (str): Encoding of the JSON file (default: utf-8).

    Returns:
        dict or None: Dictionary representation of the JSON data, or None on error.
    """
    try:
        with open(strFilePath, "r", encoding=strEncoding) as fileObj:
            return json.load(fileObj)
    except json.JSONDecodeError as decodeErr:
        logger.error(f"Error decoding JSON file {strFilePath}: {decodeErr}")
        return None
    except FileNotFoundError:
        logger.error(f"File not found: {strFilePath}")
        return None
    except Exception as genErr:
        logger.error(f"An error occurred while loading the JSON file: {genErr}")
        return None


if __name__ == "__main__":
    # Example usage / quick test
    strTxtFile = "resource/strSystemPrompt.txt"  # Replace with your file path

    try:
        strContent = ReadTxtFile(strTxtFile)
        print(strContent)
    except Exception as e:
        print(f"An error occurred: {e}")
