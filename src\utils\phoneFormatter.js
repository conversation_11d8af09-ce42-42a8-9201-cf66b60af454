/**
 * Formats phone numbers based on their length
 * @param {string} phoneNumber - The phone number to format
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return 'Not Mentioned';

  // Remove all non-digit characters
  let digits = phoneNumber.replace(/\D/g, '');

  // Remove leading 0 if present
  if (digits.startsWith('0')) {
    digits = digits.substring(1);
  }

  // Handle different phone number lengths
  if (digits.length === 10) {
    // Format as XXXXX XXXXX
    return `${digits.slice(0, 5)} ${digits.slice(5)}`;
  } else if (digits.length === 9) {
    // For 9-digit numbers (after removing leading 0), format as XXXXX XXXX
    return `${digits.slice(0, 5)} ${digits.slice(5)}`;
  } else if (digits.length === 12) {
    // Format as XX XXXXX XXXXX
    return `${digits.slice(0, 2)} ${digits.slice(2, 7)} ${digits.slice(7)}`;
  } else if (digits.length === 11) {
    // For 11-digit numbers (after removing leading 0), format as XX XXXXX XXXX
    return `${digits.slice(0, 2)} ${digits.slice(2, 7)} ${digits.slice(7)}`;
  } else {
    // Return original if it doesn't match expected lengths
    return phoneNumber;
  }
};
