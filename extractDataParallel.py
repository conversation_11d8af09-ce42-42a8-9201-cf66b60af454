# scripts/callApi.py
from datetime import datetime
import hashlib
import json
import os
import logging
import traceback
import shutil
import asyncio
from openai import OpenAI
from concurrent.futures import ThreadPoolExecutor, as_completed
from utils import MloadConfig, MloadSystemPrompt, MloadResponseFormat, MsetupLogging
from typing import Dict, Any, Union
from AWS_Async import extractByAwsTextract
from helperMongoDb import MongoDBClient

# NOTE
# This file is for Parag Traders vendor only
# Assumes PDF to text conversion file is inside the folder of the with same name file + "_Combined.txt" (abc.pdf is inside abc folder with abc_Combined.txt name)

# Constants
PROCESSED_FOLDER_NAME = "processed"
GPT_RESPONSE_SUFFIX = "_gptResponse"
TEXT_CONVERSION_SUFFIX = "_strUserPrompt"


class CExtractByOpenai:
    def __init__(self, strFilePath, strVendorName="Resume_Schema") -> None:

        self.m_config: Dict[str, Any] = MloadConfig()

        self.client = OpenAI()
        
        self.m_strFilePath = strFilePath
        self.m_strFolderPath = os.path.dirname(self.m_strFilePath)
        self.m_strFileName = os.path.basename(strFilePath)
        self.m_strFileNameWithoutExtension = os.path.splitext(self.m_strFileName)[0]
        self.m_strVendorName = strVendorName

        self.m_strSystemPromptOfAllVendors = self.m_config.get("systemPromptFilePath")
        self.m_strResponseFormatFilePath = self.m_config.get("responseFormatFilePath")

        self.m_apiResponsePath = self.m_config.get("apiResponsesPath")

    def calculate_checksum(self):
        sha256_hash = hashlib.sha256()  # You can choose a different algorithm (e.g., md5, sha1)
        with open(self.m_strFilePath, "rb") as f:
            # Read file in chunks to handle large files
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        self.checksum = sha256_hash.hexdigest()
    
    def MloadConfigurations(self):
        
        strTextFilePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension, self.m_strFileNameWithoutExtension+TEXT_CONVERSION_SUFFIX+".txt")
        print("the new file path is  : " ,strTextFilePath)
        # Extract text with AWS Textraxt if not present
        if not os.path.exists(strTextFilePath):
            asyncio.run(extractByAwsTextract(self.m_strFilePath))

        # Load the user prompt
        with open(strTextFilePath, 'r') as file:
            self.m_strUserContentStructured = file.read()

        self.m_strSystemPrompt = self.m_strSystemPromptOfAllVendors

        # Load the response format
        with open(self.m_strResponseFormatFilePath, 'r') as file:
            self.m_strResponseFormatOfAllVendors = json.load(file)

        self.m_strResponseFormat = self.m_strResponseFormatOfAllVendors[self.m_strVendorName]
        
        # Open the file in read mode
        with open(self.m_strSystemPrompt, 'r') as file:
            self.m_strSystemPrompt = file.read()  
    
    def McallOpenAiApi(self):

        # try:
        print("------------- Started extraction by OpenAI ------------------")
        print("Model: gpt-4.1-2025-04-14")
        self.objResponse = self.client.beta.chat.completions.parse(
                model="gpt-4.1-2025-04-14",
                messages=[
                    {"role": "system", "content": self.m_strSystemPrompt},
                    {"role": "user", "content": str(self.m_strUserContentStructured)}
                ],
                response_format=self.m_strResponseFormat,
                temperature= 0,
                max_completion_tokens=16384,
                seed=33
            )
        
        print("------------- OpenAI Extraction completed ------------------")
        return self.objResponse
    
    

    def MSaveResponse(self):
        
        mongo_client = MongoDBClient()
        strResponseFolderPath = os.path.join(self.m_apiResponsePath, self.m_strVendorName)
        os.makedirs(strResponseFolderPath, exist_ok=True)
        strResponseFileName = self.m_strFileNameWithoutExtension + GPT_RESPONSE_SUFFIX + ".json"
        strResponseFilePath = os.path.join(strResponseFolderPath, strResponseFileName)

        with open(strResponseFilePath, "w") as json_file:
            json.dump(self.objResponse.model_dump(), json_file, indent=4)

        dictContent = json.loads(self.objResponse.choices[0].message.content)
        
        record = {
                    **dictContent,  
                    "checksum": self.checksum,
                    "timestamp": datetime.now()
                }
        mongo_client.insert_record_withTime_checksum(record)

    def MMoveFile(self):
        
        # Moving pdf file
        strSourcePath = self.m_strFilePath
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        os.makedirs(strDestinationPath, exist_ok=True)
        shutil.move(strSourcePath, strDestinationPath)

        # moving folder of pdf file which has text and tables data
        strSourcePath = os.path.join(self.m_strFolderPath, self.m_strFileNameWithoutExtension)
        strDestinationPath = os.path.join(self.m_strFolderPath, PROCESSED_FOLDER_NAME)
        shutil.move(strSourcePath, strDestinationPath)



class CProcessDocument:

    def __init__(self, strVendorName="1_concor") -> None:
        self.m_dictConfig = MloadConfig()      # loads config in dict
        self.strVendorName = strVendorName


    def MprocessDocument(self, strFilePath) -> None:

        print(f"Processing {strFilePath}")
        objCExtractByOpenai = CExtractByOpenai(strFilePath, strVendorName=self.strVendorName)
        strFileName = os.path.basename(strFilePath)
        strFileNameWithoutExtension = os.path.splitext(strFileName)[0]
        objCExtractByOpenai.calculate_checksum()
        objCExtractByOpenai.MloadConfigurations()
        objCExtractByOpenai.McallOpenAiApi()
        objCExtractByOpenai.MSaveResponse()
        objCExtractByOpenai.MMoveFile()





    def MprocessAllDocuments(self, iFilesToProcess=1) -> None:

        strFolderPathToProcess = os.path.join(self.m_dictConfig.get("inputDataPath"), self.strVendorName)
        intTotalFilesProcessed = 0

        listFiles = os.listdir(strFolderPathToProcess)
        with ThreadPoolExecutor() as executor:
            futures = []
            for file in listFiles:
                if file.lower().endswith(".pdf"):
                    strFilePath = os.path.join(strFolderPathToProcess, file)
                    futures.append(executor.submit(self.MprocessDocument, strFilePath))
            for future in as_completed(futures):
                try:
                    future.result()
                    intTotalFilesProcessed += 1
                    if intTotalFilesProcessed >= iFilesToProcess:
                        break
                except Exception as e:
                    logging.error(e)


if __name__ == "__main__":

    objCProcessDocument = CProcessDocument(strVendorName="Resume_Schema")          
    objCProcessDocument.MprocessAllDocuments(iFilesToProcess=10)     # processes all file
    