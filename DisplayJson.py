from textwrap import dedent
import streamlit as st
import json
from helperMongoDb import MongoDBClient
import time
from helperMongoDb import MongoDBClient
import time

# List of pastel colors with their hex codes
pastel_colors = [
    "#ffedf1",  # Pastel Pink
    "#ebf9ff",  # Baby Blue
    "#eafff4",  # Mint Green
    "#eeeeff",  # Lavender
    "#fff9ef",  # Peach
]
# Initialize color index as an instance variable
color_index = 0


# List of pastel colors with their hex codes
pastel_colors = [
    "#ffedf1",  # Pastel Pink
    "#ebf9ff",  # Baby Blue
    "#eafff4",  # Mint Green
    "#eeeeff",  # Lavender
    "#fff9ef",  # Peach
]
# Initialize color index as an instance variable
color_index = 0

class CDisplayResume:

  # @staticmethod
  # def MSProcessDate(date:int):
  #   if len(str(date)) == 4:
      # return date
  @staticmethod
  def format_date(date):
    date = str(date)
    """Format a date string from YYYYMMDD or YYYYMM to YYYY/MM/DD or YYYY/MM."""
    if not date:  # Handle empty or None case
        return date
    if len(str(date)) == 8:  # YYYYMMDD format
      # return f"{date[:4]}/{date[4:6]}/{date[6:]}"
      return f"{date[6:]}/{date[4:6]}/{date[:4]}"
    elif len(str(date)) == 6:  # YYYYMM format
        return f"{date[4:]}/{date[:4]}"
    return date  # Return as-is if invalid length

  @staticmethod
  def format_date_workExp(date):
    date = str(date)
    """Format a date string from YYYYMMDD or YYYYMM to YYYY/MM/DD or YYYY/MM."""
    if not date:  # Handle empty or None case
        return date
    elif len(str(date)) == 6:  # YYYYMM format
        return f"{date[:2]}/{date[2:]}" #MMYYYY
    elif len(str(date)) == 5:  # YYYYMM format
        return f"{date[:1]}/{date[1:]}" #MYYYY
    return date  # Return as-is if invalid length

  @staticmethod
  def MSGenerateResumeHTML(resume_data):


      # Extract the 'Resume' object
      resume = resume_data.get("Resume", {})
      # Pull out each section
      personal_info = resume.get("PersonalInformation", {})
      objective = resume.get("Objective", "")
      education_list = resume.get("Education", [])
      work_experience_list = resume.get("WorkExperience", [])
      total_experience = resume.get("TotalWorkExperienceInYears", 0)
      skills_list = resume.get("Skills", [])
      certifications_list = resume.get("Certifications", [])
      achievements_list = resume.get("Achievements", [])
      languages_list = resume.get("Languages", [])
      projects_list = resume.get("Projects", [])


      # ------------------------------------------------------
      # Date
      # Modular date formatting function


      # Example usage in your context
      personal_info = resume.get("PersonalInformation", {})
      ach = resume.get("Achievements", {})  # Assuming this structure
      cert = resume.get("Certificates", {})  # Assuming this structure

      # Format all dates using the modular function
      formatted_birth_date = CDisplayResume.format_date(personal_info.get("BirthDate", ""))
      formatted_certificate_issue_date = CDisplayResume.format_date(cert.get("IssueDate", ""))

      # ------------------------------------------------------
      # 1. PERSONAL INFORMATION
      # ------------------------------------------------------
      personal_info_html = dedent(f"""
      <table style="
          width:100%;
          border-collapse: collapse;
          margin-bottom: 1.5rem;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      ">
        {"".join(f'''
        <tr>
            <td style="
                border: none;
                padding: 12px 16px;
                background: #f8f9fa;
                font-weight: 600;
                color: #495057;
                border-bottom: 1px solid #e9ecef;
            "><strong>Full Name</strong></td>
            <td style="
                border: none;
                padding: 12px 16px;
                color: #212529;
                border-bottom: 1px solid #e9ecef;
            ">{personal_info["FullName"]}</td>
        </tr>''' if personal_info.get("FullName") else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>Gender</strong></td>
          <td style="border:1px solid black; padding:8px;">{personal_info["Gender"]}</td>
        </tr>''' if personal_info.get("Gender") else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>Birth Date</strong></td>
          <td style="border:1px solid black; padding:8px;">{formatted_birth_date}</td>
        </tr>''' if formatted_birth_date else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>Email</strong></td>
          <td style="border:1px solid black; padding:8px;">{personal_info["Email"]}</td>
        </tr>''' if personal_info.get("Email") else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>Address</strong></td>
          <td style="border:1px solid black; padding:8px;">{personal_info["Address"]}</td>
        </tr>''' if personal_info.get("Address") else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>Contact Number</strong></td>
          <td style="border:1px solid black; padding:8px;">{personal_info["ContactNumber"]}</td>
        </tr>''' if personal_info.get("ContactNumber") else "")}

        {"".join(f'''
        <tr>
          <td style="border:1px solid black; padding:8px;"><strong>LinkedIn Profile</strong></td>
          <td style="border:1px solid black; padding:8px;">{personal_info["LinkedInProfile"]}</td>
        </tr>''' if personal_info.get("LinkedInProfile") else "")}

      </table>
      """)

      # ------------------------------------------------------
      # 2. OBJECTIVE
      # ------------------------------------------------------
      if objective :
        objective_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th style="border:1px solid black; padding:8px; font-size: 32px;">
              Objective
            </th>
          </tr>
          <tr>
            <td style="border:1px solid black; padding:8px;">{objective}</td>
          </tr>
        </table>
        """)
      else:
        objective_html = ""

      # ------------------------------------------------------
      # 3. EDUCATION
      # ------------------------------------------------------
      if education_list :
        education_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th colspan="4" style="border:1px solid black; padding:8px; font-size: 32px;">
              Education
            </th>
          </tr>
          <tr>
            <th style="border:1px solid black; padding:8px;">Degree</th>
            <th style="border:1px solid black; padding:8px;">Institution</th>
            <th style="border:1px solid black; padding:8px;">Graduation Year</th>
            <th style="border:1px solid black; padding:8px;">GPA/Marks/%</th>
          </tr>
        """)
        for edu in education_list:
            education_html += dedent(f"""
            <tr>
              <td style="border:1px solid black; padding:8px;">{edu.get("Degree", "")}</td>
              <td style="border:1px solid black; padding:8px;">{edu.get("Institution", "")}</td>
              <td style="border:1px solid black; padding:8px;">{edu.get("GraduationYear", "")}</td>
              <td style="border:1px solid black; padding:8px;">{edu.get("GPA/Marks/%", "")}</td>
            </tr>
            """)
        education_html += "</table>"
      else:
         education_html = ""

      # ------------------------------------------------------
      # 4. WORK EXPERIENCE
      # ------------------------------------------------------
      if work_experience_list :
        work_experience_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th colspan="5" style="border:1px solid black; padding:8px; font-size: 32px;">
              Work Experience
            </th>
          </tr>
          <tr>
            <th style="border:1px solid black; padding:8px;">Company Name</th>
            <th style="border:1px solid black; padding:8px;">Role</th>
            <th style="border:1px solid black; padding:8px;">Start Year</th>
            <th style="border:1px solid black; padding:8px;">End Year</th>
          <!--  <th style="border:1px solid black; padding:8px;">Description / Responsibility</th> -->
          </tr>
        """)
        for we in work_experience_list:
          formatted_we_start_date = CDisplayResume.format_date_workExp(we.get("StartYear", ""))
          formatted_we_end_date = CDisplayResume.format_date_workExp(we.get("EndYear", ""))
          work_experience_html += dedent(f"""
          <tr>
            <td style="border:1px solid black; padding:8px;">{we.get("CompanyName", "")}</td>
            <td style="border:1px solid black; padding:8px;">{we.get("Role", "")}</td>
            <td style="border:1px solid black; padding:8px;">{formatted_we_start_date}</td>
            <td style="border:1px solid black; padding:8px;">{formatted_we_end_date}</td>
          <!--  <td style="border:1px solid black; padding:8px;">{we.get("Description/Responsibility", "")}</td> -->
          </tr>
          """)
        work_experience_html += "</table>"
      else:
        work_experience_html = ""

      # ------------------------------------------------------
      # 5. TOTAL EXPERIENCE (TotalWorkExperienceInYears)
      # ------------------------------------------------------
      if total_experience :
        experience_years_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th style="border:1px solid black; padding:8px; font-size: 32px;">
              Total Work Experience (Years)
            </th>
          </tr>
          <tr>
            <td style="border:1px solid black; padding:8px;">{total_experience}</td>
          </tr>
        </table>
        """)
      else:
         experience_years_html = ""

      # ------------------------------------------------------
      # 6. SKILLS (array of strings)
      # ------------------------------------------------------
      # Option A: Table approach
      if skills_list :
        skills_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th style="border:1px solid black; padding:8px; font-size: 32px;">
              Skills
            </th>
          </tr>
        """)
        for skill in skills_list:
            skills_html += dedent(f"""
            <tr>
              <td style="border:1px solid black; padding:8px;">{skill}</td>
            </tr>
            """)
        skills_html += "</table>"
      else:
         skills_html = ""

      # ------------------------------------------------------
      # 7. CERTIFICATIONS
      # ------------------------------------------------------
      if certifications_list :
        certifications_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th colspan="4" style="border:1px solid black; padding:8px; font-size: 32px;">
              Certifications
            </th>
          </tr>
          <tr>
            <th style="border:1px solid black; padding:8px;">Certification Name</th>
            <th style="border:1px solid black; padding:8px;">Issuing Organization</th>
            <th style="border:1px solid black; padding:8px;">Issue Date</th>
            <th style="border:1px solid black; padding:8px;">Expiry Date</th>
          </tr>
        """)
        for cert in certifications_list:
          formatted_certificate_issue_date = CDisplayResume.format_date(cert.get("IssueDate", ""))
          formatted_certificate_expiry_date = CDisplayResume.format_date(cert.get("ExpiryDate", ""))

          certifications_html += dedent(f"""
          <tr>
            <td style="border:1px solid black; padding:8px;">{cert.get("CertificationName", "")}</td>
            <td style="border:1px solid black; padding:8px;">{cert.get("IssuingOrganization", "")}</td>
            <td style="border:1px solid black; padding:8px;">{formatted_certificate_issue_date}</td>
            <td style="border:1px solid black; padding:8px;">{formatted_certificate_expiry_date}</td>
          </tr>
          """)
        certifications_html += "</table>"
      else:
        certifications_html = ""

      # ------------------------------------------------------
      # 8. ACHIEVEMENTS
      # ------------------------------------------------------
      if achievements_list :
        achievements_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th colspan="3" style="border:1px solid black; padding:8px; font-size: 32px;">
              Achievements
            </th>
          </tr>
          <tr>
            <th style="border:1px solid black; padding:8px;">Achievement Name</th>
            <th style="border:1px solid black; padding:8px;">Issuing Organization</th>
            <th style="border:1px solid black; padding:8px;">Issue Date</th>
          </tr>
        """)
        for ach in achievements_list:
            formatted_achievements_issue_date = CDisplayResume.format_date(ach.get("IssueDate", ""))
            achievements_html += dedent(f"""
            <tr>
              <td style="border:1px solid black; padding:8px;">{ach.get("AchievementName", "")}</td>
              <td style="border:1px solid black; padding:8px;">{ach.get("IssuingOrganization", "")}</td>
              <td style="border:1px solid black; padding:8px;">{formatted_achievements_issue_date}</td>
            </tr>
            """)
        achievements_html += "</table>"
      else:
         achievements_html = ""

      # ------------------------------------------------------
      # 9. LANGUAGES
      # ------------------------------------------------------
      if languages_list:
        languages_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th style="border:1px solid black; padding:8px; font-size: 32px;">
              Languages
            </th>
          </tr>
        """)
        for lang in languages_list:
            languages_html += dedent(f"""
            <tr>
              <td style="border:1px solid black; padding:8px;">{lang}</td>
            </tr>
            """)
        languages_html += "</table>"
      else:
         languages_html = ""

      # ------------------------------------------------------
      # 10. PROJECTS
      # ------------------------------------------------------
      if projects_list :
        projects_html = dedent(f"""
        <table style="width:100%; border:1px solid #fff; border-collapse: collapse; margin-bottom: 20px;">
          <tr>
            <th colspan="4" style="border:1px solid black; padding:8px; font-size: 32px;">
              Projects
            </th>
          </tr>
          <tr>
            <th style="border:1px solid black; padding:8px;">Project Name</th>
            <th style="border:1px solid black; padding:8px;">Description</th>
            <th style="border:1px solid black; padding:8px;">Technologies Used</th>
            <th style="border:1px solid black; padding:8px;">Role</th>
          </tr>
        """)
        for project in projects_list:
            # Convert technologies to a comma-separated string or bullet list
            tech_list = project.get("TechnologiesUsed", [])
            tech_str = ", ".join(tech_list)

            projects_html += dedent(f"""
            <tr>
              <td style="border:1px solid black; padding:8px;">{project.get("ProjectName", "")}</td>
              <td style="border:1px solid black; padding:8px;">{project.get("Description", "")}</td>
              <td style="border:1px solid black; padding:8px;">{tech_str}</td>
              <td style="border:1px solid black; padding:8px;">{project.get("Role", "")}</td>
            </tr>
            """)
        projects_html += "</table>"
      else:
         projects_html = ""

      # ------------------------------------------------------
      # COMBINE ALL SECTIONS
      # ------------------------------------------------------

      global color_index

      # Get the current color
      current_color = pastel_colors[color_index]

      full_resume_html = dedent(f"""
          <div style="
              width:100%;
              background: linear-gradient(135deg, {current_color} 0%, rgba(255,255,255,0.9) 100%);
              padding: 1.5rem;
              margin-bottom: 1rem;
              border-radius: 12px;
              border: 1px solid rgba(102, 126, 234, 0.1);
              box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
          ">
      """)

      resume_html = (
          personal_info_html
          + objective_html
          + education_html
          + work_experience_html
          + experience_years_html
          + skills_html
          + certifications_html
          + achievements_html
          + languages_html
          + projects_html
      )

      # Close the outer container
      full_resume_html += resume_html + "</div>"

      # Increment the counter, looping back to 0
      color_index = (color_index + 1) % len(pastel_colors)
      # Return the complete HTML
      return full_resume_html


  @staticmethod
  def delete_resume_callback(resume_id, personal_info):
    mongoclient = MongoDBClient()
    if mongoclient.delete_data_with_id(resume_id):
      # Store deleted resume ID in session state
      if "deleted_resumes" not in st.session_state:
          st.session_state["deleted_resumes"] = set()

      st.session_state["deleted_resumes"].add(resume_id)
      st.toast(f"Resume Delete {personal_info.get('FullName', '')}', icon='✅'")
      time.sleep(1.5)
    else:
       st.toast(f"Resume Not Delete : {personal_info.get('FullName', '')}', icon='❌'")
    st.rerun()

  @staticmethod
  def display_resume_with_delete(resume_data):
      # resume_id = resume_data.get("_id")
      resume_id = str(resume_data.get("_id"))  # Ensure ID is string for session tracking
      # Create a container to hold both the delete button and the resume HTML

      resume = resume_data.get("Resume", {})
      personal_info = resume.get("PersonalInformation", {})

      unique_key = f"delete_{resume_id}"

      # Initialize session state to track deleted resumes
      if "deleted_resumes" not in st.session_state:
          st.session_state["deleted_resumes"] = set()

      # If this resume was deleted, do not display it
      if resume_id in st.session_state["deleted_resumes"]:
          return  # Skip rendering this resume

      # Enhanced resume card container
      with st.container():
        st.markdown(
            """
            <div class="resume-card fade-in-up">
            """,
            unsafe_allow_html=True
        )

        # Create columns for name and delete button with better styling
        col1, col2 = st.columns([4, 1])

        with col1:
            st.markdown(
                f"""
                <h3 style="color: #667eea; margin: 0; font-weight: 600;">
                    👤 {personal_info.get('FullName', 'Unknown')}
                </h3>
                """,
                unsafe_allow_html=True
            )

        with col2:
            if st.button("🗑️ Delete", key=unique_key, help="Delete this resume", use_container_width=True):
                CDisplayResume.delete_resume_callback(resume_id, personal_info)

        # Add a separator
        st.markdown("<hr style='margin: 1rem 0; border: 1px solid #e9ecef;'>", unsafe_allow_html=True)

        # Now display the resume content
        resume_html = CDisplayResume.MSGenerateResumeHTML(resume_data)
        st.markdown(resume_html, unsafe_allow_html=True)

        # Close the resume card container
        st.markdown("</div>", unsafe_allow_html=True)


  @staticmethod
  def MSDisplayTwoResumesSideBySide(file_paths_list ):
      """
      Renders two resumes side by side in Streamlit "cards".
      """

      # Loop through the data list two at a time
      for i in range(0, len(file_paths_list), 2):
          cols = st.columns(2)

          # Left resume
          with cols[0]:
              with open(file_paths_list[i], "r", encoding="utf-8") as f1:
                  data1 = json.load(f1)
              CDisplayResume.display_resume_with_delete(data1)

          # Right resume, if available
          if i + 1 < len(file_paths_list):
              with cols[1]:
                  with open(file_paths_list[i+1], "r", encoding="utf-8") as f2:
                      data2 = json.load(f2)
                  CDisplayResume.display_resume_with_delete(data2)
          else:
              cols[1].empty()

def main():
    st.set_page_config(page_title="Compare Two Resumes", layout="wide")
    st.title("Compare Two Resumes Side by Side")

    # # 1. Load your two JSON files (static paths)
    # resume_file_1 = "\Data\ReadableResponseData\Resume_Schema\Geetnajali Dembla_ReadableResponseData.json"
    # resume_file_2 = "\Data\ReadableResponseData\Resume_Schema\Resume Rajendra Sir (1)_ReadableResponseData.json"
    # resume_file_3 = "\Data\ReadableResponseData\Resume_Schema\Shirin Sadriwala profile_ReadableResponseData.json"
    # file_paths_list = [resume_file_1, resume_file_2, resume_file_3, resume_file_1, resume_file_2, resume_file_3, resume_file_3]
    # # 2. Display them side by side
    # CDisplayResume.MSDisplayTwoResumesSideBySide(file_paths_list)


if __name__ == "__main__":
    main()
