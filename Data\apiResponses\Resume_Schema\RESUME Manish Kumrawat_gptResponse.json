{"id": "chatcmpl-BefxoCp6SQHfofJmHomtbfAGjeR08", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"Manish Kumrawat\",\"Gender\":\"Male\",\"BirthDate\":19750615,\"Email\":\"<EMAIL>\",\"Address\":\"19, Manavata Nagar, Indore (M.P.)\",\"ContactNumber\":\"+91 19754902643\",\"LinkedInProfile\":\"\"},\"Objective\":\"To Obtain Teaching Position in an elementary School.\",\"Education\":[{\"Degree\":\"B.Ed.\",\"Institution\":\"Jiwaji University, Gwalior\",\"GraduationYear\":\"2021\",\"GPA/Marks/%\":69.3},{\"Degree\":\"M.Sc.\",\"Institution\":\"MMYVV\",\"GraduationYear\":\"2017\",\"GPA/Marks/%\":69.2},{\"Degree\":\"B.Sc.\",\"Institution\":\"MMYVV\",\"GraduationYear\":\"2015\",\"GPA/Marks/%\":68.0}],\"WorkExperience\":[{\"CompanyName\":\"Riddhi Siddhi international school, Rajgarh\",\"Role\":\"Math's/Physics Teacher\",\"StartYear\":20200401,\"EndYear\":20231001,\"Description/Responsibility\":\"Providing the materials to the students and using their skills for developing that material by using their skills. Evaluating the performance of the students and providing them the essential information about where they should hard work. Showing them the various methods of arts and planning the strategies for developing students in different genres. Assigning the projects to the students and by this way examining their progress.\"},{\"CompanyName\":\"Riddhi Siddhi international school, Rajgarh\",\"Role\":\"Vice Principal UDT (Math's/Physics)\",\"StartYear\":2023,\"EndYear\":20231001,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Gayatri Shakti Peeth H.S. School\",\"Role\":\"Principal & UDT(Math's/Physics)\",\"StartYear\":2020,\"EndYear\":2023,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Anshul vidhya mandir (C.B.S.E.) Jobat\",\"Role\":\"Principal & UDT(Math's/Physics)\",\"StartYear\":2019,\"EndYear\":2020,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Bafna high sec.school (C.B.S.E.) School\",\"Role\":\"UDT(Math's/Physics)\",\"StartYear\":2018,\"EndYear\":2019,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Gurukul school bagh\",\"Role\":\"UDT(Math's/Physics)\",\"StartYear\":2017,\"EndYear\":2018,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Presistent High School Bagh Trinty\",\"Role\":\"UDT(Math's/Physics)\",\"StartYear\":2016,\"EndYear\":2017,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"G.D.Somani Public School, Khandwa road indore trinty\",\"Role\":\"UDT(Math's/Physics) & Hostel warden\",\"StartYear\":2011,\"EndYear\":2016,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"St. Joseph High School, Nanda nagar Indore trinty (C.B.S.E.)\",\"Role\":\"UDT (Math's/Physics)\",\"StartYear\":2005,\"EndYear\":2011,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Janta High Secondary School, Vinoba Nagar Indore trinty\",\"Role\":\"UDT (Math's/Physics)\",\"StartYear\":2001,\"EndYear\":2005,\"Description/Responsibility\":\"\"},{\"CompanyName\":\"Archana High School, Tilak Nagar Indore, Trinty\",\"Role\":\"UDT(Math's/Physics)\",\"StartYear\":1998,\"EndYear\":2001,\"Description/Responsibility\":\"\"}],\"TotalWorkExperienceInYears\":25,\"Skills\":[\"Profound Knowledge of Math's\",\"Ability to understand the abstract concepts\",\"Ability to perceive the creativity in the students\",\"Capacity to guide students in various arts like Painting, Sketching, and designing\",\"Ability to prepare lessons and decide the goal of Projects\",\"Ability to demonstrate lessons to the students and evaluate the performance of the students\",\"Capacity to confer with the parents and analyze the progress of the student to them\",\"Ability to help students to solve the problems and communicate with a counselor regarding that\",\"Good Knowledge about arranging the field trips i.e. place of trip, museum\"],\"Certifications\":[],\"Achievements\":[],\"Languages\":[\"Hindi\",\"English\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null, "annotations": []}}], "created": 1749034128, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_07871e2ad8", "usage": {"completion_tokens": 901, "prompt_tokens": 4045, "total_tokens": 4946, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 1152}}}