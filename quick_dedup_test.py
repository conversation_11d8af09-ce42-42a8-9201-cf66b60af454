"""
Quick test to verify deduplication is working.
"""

import asyncio
from chromaMongoSearch import search_resumes

async def test():
    print("🔍 Quick Deduplication Test")
    print("=" * 40)
    
    results = await search_resumes("Find me a female teacher who teaches hindi")
    
    # Check for duplicates
    mongodb_ids = [r['mongodb_id'] for r in results['results']]
    unique_ids = set(mongodb_ids)
    
    print(f"Total results: {len(results['results'])}")
    print(f"Unique MongoDB IDs: {len(unique_ids)}")
    print(f"Deduplication: {'✅ Working' if len(mongodb_ids) == len(unique_ids) else '❌ Failed'}")
    
    print("\nResults:")
    for r in results['results']:
        print(f"  {r['rank']}. {r['full_name']} ({r['embedding_type']}) - ID: {r['mongodb_id'][-4:]}")

asyncio.run(test())
