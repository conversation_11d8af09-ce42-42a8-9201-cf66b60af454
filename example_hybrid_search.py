"""
Example usage of the ChromaDB + MongoDB Hybrid Search System.

This demonstrates how to use the hybrid search for finding resumes
using natural language queries.
"""

import asyncio
import json
from chromaMongoSearch import ChromaMongoSearchEngine, search_resumes

async def example_basic_search():
    """Example of basic search usage."""
    
    print("🔍 Example: Basic Hybrid Search")
    print("=" * 50)
    
    # Simple search using convenience function
    query = "Find software engineers with Python and machine learning experience"
    
    print(f"Searching for: {query}")
    print()
    
    results = await search_resumes(query, top_k=5)
    
    # Display results
    print(f"📊 Search Results:")
    print(f"   Query: {results['query']}")
    print(f"   Optimized: {results['optimized_query']}")
    print(f"   Total Results: {results['total_results']}")
    print()
    
    if results['results']:
        print("🏆 Top Matches:")
        for result in results['results']:
            print(f"   {result['rank']}. {result['full_name']} ({result['email']})")
            print(f"      Similarity Score: {result['similarity_score']:.3f}")
            print(f"      Match Distance: {result['match_distance']:.3f}")
            print(f"      Embedding Type: {result['embedding_type']}")
            print(f"      MongoDB ID: {result['mongodb_id']}")
            print(f"      Preview: {result['document_preview'][:150]}...")
            print()
    else:
        print("No results found.")

async def example_advanced_search():
    """Example of advanced search with custom parameters."""
    
    print("\n🔬 Example: Advanced Search with Custom Engine")
    print("=" * 50)
    
    # Initialize search engine with custom settings
    search_engine = ChromaMongoSearchEngine(
        database_name="dbProductionV2",
        collection_name="collectionResumeV2",
        chroma_host="localhost",
        chroma_port=8000
    )
    
    # Multiple search queries
    queries = [
        "Find data scientists with PhD in machine learning",
        "Looking for frontend developers with React and TypeScript",
        "Need project managers with agile and scrum certification",
        "Search for biotechnology researchers with lab experience"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 Search {i}: {query}")
        print("-" * 40)
        
        results = await search_engine.search(query, top_k=3)
        
        print(f"Results: {results['total_results']}")
        print(f"MongoDB IDs: {results['mongodb_ids_found']}")
        print(f"ChromaDB Docs: {results['chromadb_docs_found']}")
        
        if results['results']:
            top_result = results['results'][0]
            print(f"Top Match: {top_result['full_name']} (Score: {top_result['similarity_score']:.3f})")
        
        if results.get('error'):
            print(f"Error: {results['error']}")

async def example_search_analysis():
    """Example showing detailed search analysis."""
    
    print("\n📈 Example: Search Analysis")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    query = "Find experienced Python developers with AI and machine learning skills"
    
    print(f"Analyzing search: {query}")
    print()
    
    results = await search_engine.search(query, top_k=10)
    
    # Analyze results by embedding type
    if results['results']:
        embedding_types = {}
        similarity_scores = []
        
        for result in results['results']:
            emb_type = result['embedding_type']
            embedding_types[emb_type] = embedding_types.get(emb_type, 0) + 1
            similarity_scores.append(result['similarity_score'])
        
        print("📊 Results Analysis:")
        print(f"   Total Results: {len(results['results'])}")
        print(f"   Average Similarity: {sum(similarity_scores) / len(similarity_scores):.3f}")
        print(f"   Best Match Score: {max(similarity_scores):.3f}")
        print(f"   Worst Match Score: {min(similarity_scores):.3f}")
        print()
        
        print("📋 Results by Embedding Type:")
        for emb_type, count in sorted(embedding_types.items()):
            print(f"   {emb_type}: {count} results")
        print()
        
        print("🏆 Top 3 Results:")
        for result in results['results'][:3]:
            print(f"   {result['rank']}. {result['full_name']}")
            print(f"      Type: {result['embedding_type']}")
            print(f"      Score: {result['similarity_score']:.3f}")
            print(f"      File: {result['original_filename']}")
            print()

async def example_search_comparison():
    """Example comparing different query formulations."""
    
    print("\n🔄 Example: Query Comparison")
    print("=" * 50)
    
    search_engine = ChromaMongoSearchEngine()
    
    # Different ways to ask for the same thing
    query_variations = [
        "Python developer",
        "Software engineer with Python experience",
        "Find programmers who know Python programming language",
        "Looking for developers skilled in Python and software development"
    ]
    
    print("Comparing query variations for Python developers:")
    print()
    
    for i, query in enumerate(query_variations, 1):
        print(f"Query {i}: {query}")
        
        results = await search_engine.search(query, top_k=3)
        
        print(f"   Optimized to: {results['optimized_query']}")
        print(f"   Results: {results['total_results']}")
        
        if results['results']:
            avg_score = sum(r['similarity_score'] for r in results['results']) / len(results['results'])
            print(f"   Avg Similarity: {avg_score:.3f}")
            print(f"   Top Match: {results['results'][0]['full_name']} ({results['results'][0]['similarity_score']:.3f})")
        
        print()

def example_search_tips():
    """Display tips for effective searching."""
    
    print("\n💡 Search Tips and Best Practices")
    print("=" * 50)
    
    tips = [
        "Use specific job titles: 'software engineer', 'data scientist', 'project manager'",
        "Include relevant skills: 'Python', 'machine learning', 'React', 'agile'",
        "Mention experience levels: 'senior', 'experienced', 'junior', 'entry-level'",
        "Add domain expertise: 'healthcare', 'finance', 'e-commerce', 'biotechnology'",
        "Include certifications: 'AWS certified', 'PMP', 'Scrum master'",
        "Use education keywords: 'PhD', 'Masters', 'Computer Science', 'Engineering'",
        "Combine multiple criteria: 'Python developer with machine learning and 5 years experience'"
    ]
    
    print("🎯 Effective Query Examples:")
    for i, tip in enumerate(tips, 1):
        print(f"   {i}. {tip}")
    
    print("\n🚫 What to Avoid:")
    avoid_tips = [
        "Very generic terms: 'good candidate', 'smart person'",
        "Overly complex sentences with multiple unrelated requirements",
        "Spelling mistakes in technical terms",
        "Using only company names or personal names (these are excluded from search)"
    ]
    
    for i, tip in enumerate(avoid_tips, 1):
        print(f"   {i}. {tip}")

async def main():
    """Main example function."""
    
    print("🚀 ChromaDB + MongoDB Hybrid Search Examples")
    print("=" * 80)
    
    try:
        # Example 1: Basic search
        await example_basic_search()
        
        # Example 2: Advanced search
        await example_advanced_search()
        
        # Example 3: Search analysis
        await example_search_analysis()
        
        # Example 4: Query comparison
        await example_search_comparison()
        
        # Example 5: Search tips
        example_search_tips()
        
        print("\n" + "=" * 80)
        print("🎉 Examples completed successfully!")
        print("\nKey Features Demonstrated:")
        print("✅ Natural language query processing")
        print("✅ Two-stage hybrid search (MongoDB + ChromaDB)")
        print("✅ Vector similarity scoring")
        print("✅ Comprehensive result analysis")
        print("✅ Query optimization with OpenAI")
        
    except Exception as e:
        print(f"❌ Example failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
