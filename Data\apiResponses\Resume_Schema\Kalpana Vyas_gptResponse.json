{"id": "chatcmpl-B3eNrB7Q6DQLJIKxyEhc04UDEphBg", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": null, "message": {"content": "{\"Resume\":{\"PersonalInformation\":{\"FullName\":\"Kalpana Vyas\",\"Gender\":\"Female\",\"BirthDate\":19790112,\"Email\":\"<EMAIL>\",\"Address\":\"37/1, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, (M.P.) 452001\",\"ContactNumber\":\"+91 8602193009\",\"LinkedInProfile\":\"\"},\"Objective\":\"To be associated with a company, where I can utilize my skills, gain further experience and excellence in my technical skills while enhancing the company's productivity and reputation.\",\"Education\":[{\"Degree\":\"M.A. Yogic Science\",\"Institution\":\"BRAUSS Mhow\",\"GraduationYear\":\"\" ,\"GPA/Marks/%\":0},{\"Degree\":\"M.A.\",\"Institution\":\"GDC College, Indore\",\"GraduationYear\":\"2002\",\"GPA/Marks/%\":0},{\"Degree\":\"B.A.\",\"Institution\":\"GDC College, Indore\",\"GraduationYear\":\"2000\",\"GPA/Marks/%\":0},{\"Degree\":\"DCA\",\"Institution\":\"Arihant Institute Indore\",\"GraduationYear\":\"2006\",\"GPA/Marks/%\":0},{\"Degree\":\"Higher Secondary School\",\"Institution\":\"Govt. School No.5 Indore\",\"GraduationYear\":\"1997\",\"GPA/Marks/%\":0},{\"Degree\":\"High School\",\"Institution\":\"Govt. School No.5 Indore\",\"GraduationYear\":\"1995\",\"GPA/Marks/%\":0}],\"WorkExperience\":[{\"CompanyName\":\"Personal Yoga Classes\",\"Role\":\"Yoga Instructor\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Conducted personal yoga classes for 2 years.\"},{\"CompanyName\":\"Shree Maharshi Vidhya Mandir School, Indore\",\"Role\":\"Yoga and Sports Teacher\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Worked as a Yoga and Sports teacher for 1 year.\"},{\"CompanyName\":\"The International School Of Bombay\",\"Role\":\"Yoga Teacher and Transport Coordinator\",\"StartYear\":0,\"EndYear\":0,\"Description/Responsibility\":\"Worked as a yoga teacher and transport coordinator for 4 years.\"},{\"CompanyName\":\"Pink Flower School, Nanda Nagar\",\"Role\":\"Yoga Teacher\",\"StartYear\":202206,\"EndYear\":0,\"Description/Responsibility\":\"Working as a yoga teacher since June 2022.\"}],\"TotalWorkExperienceInYears\":7,\"Skills\":[\"Basic Computer Knowledge\",\"MS-Word\",\"MS-Excel\",\"MS-PowerPoint\"],\"Certifications\":[{\"CertificationName\":\"Assistant Yoga Teacher Training Certificate\",\"IssuingOrganization\":\"\",\"IssueDate\":20160101,\"ExpiryDate\":0},{\"CertificationName\":\"Yoga Teacher Training Certificate\",\"IssuingOrganization\":\"\",\"IssueDate\":20170101,\"ExpiryDate\":0},{\"CertificationName\":\"Pradhan Kaushal Vikas Yojna (PMKVY) Certificate\",\"IssuingOrganization\":\"Ministry of Ayush (Govt. of India)\",\"IssueDate\":20170621,\"ExpiryDate\":0},{\"CertificationName\":\"Certified Course in Yoga Sciences\",\"IssuingOrganization\":\"DAVV\",\"IssueDate\":0,\"ExpiryDate\":0}],\"Achievements\":[{\"AchievementName\":\"Participation in International Day of Yoga\",\"IssuingOrganization\":\"Ministry of Ayush (Govt. of India)\",\"IssueDate\":20170621}],\"Languages\":[\"English\",\"Hindi\"],\"Projects\":[]}}", "refusal": null, "role": "assistant", "audio": null, "function_call": null, "tool_calls": null, "parsed": null}}], "created": 1740209919, "model": "gpt-4o-2024-08-06", "object": "chat.completion", "service_tier": "default", "system_fingerprint": "fp_eb9dce56a8", "usage": {"completion_tokens": 686, "prompt_tokens": 2989, "total_tokens": 3675, "completion_tokens_details": {"accepted_prediction_tokens": 0, "audio_tokens": 0, "reasoning_tokens": 0, "rejected_prediction_tokens": 0}, "prompt_tokens_details": {"audio_tokens": 0, "cached_tokens": 0}}}