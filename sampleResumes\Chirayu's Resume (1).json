{"Resume": {"PersonalInformation": {"FullName": "<PERSON><PERSON><PERSON>", "Gender": "", "BirthDate": "30 June, 2000", "Email": "<EMAIL>", "Address": "Udaipur, INDIA", "ContactNumber": "9079743352", "LinkedInProfile": ""}, "Objective": "A driven and detail-oriented professional seeking a dynamic role as a Business Intelligence Analyst. I am known for my organizational prowess, collaboration, and dedication to delivering insightful, data-drivensolutions", "Education": [{"Degree": "B.Tech(CSE)", "Institution": "Rajasthan Technical University", "GraduationYear": "05/2021", "GPA/Marks/%": ""}], "WorkExperience": [{"CompanyName": "Global IT solutions pvt. ltd. ", "Role": "Junior Business Analyst", "StartYear": "", "EndYear": "08/2023 -pt", "Description/Responsibility": "Working as a business analyst to grow the business of the   company by creating dashboards and reports from data of students."}, {"CompanyName": "Dotsquares Technologies pvt ltd ", "Role": "Associate Programmer", "StartYear": "05/2022", "EndYear": "05/2023", "Description/Responsibility": "Collaborated seamlessly with cross-functional teams, ensuring streamlined project delivery through effective  communication and coordination, enhancing overall project efficiency. Applied technical proficiency in MEAN Stack  technologies to assist in creating and optimizing web   applications, demonstrating a hands-on approach to problem-solving and solution delivery. "}, {"CompanyName": "BOSCH Limited", "Role": "Tester (Intern)", "StartYear": "06/2019", "EndYear": "07/2019", "Description/Responsibility": "Worked as a tester on the application designed for the  organization which help to enhance the security system of    the organization by monitoring each visitor as well as the employees of the company. My work on the project was to   test the each module as well as the sub-modules, by   providing them various values, and make it easy to use to the user."}], "Skills": ["MEAN Stack,javascript,angular,node,mongodb,mongoose,python,database,Pandas,numpy,data preprocessing, data analysis,data visualization, R, machine learning,solution oriented, communication,chatgpt & other AI technologies"], "Certifications": [{"CertificationName": "", "IssuingOrganization": "", "IssueDate": "", "ExpiryDate": ""}], "Achievements": [{"AchievementName": "", "IssuingOrganization": "", "IssueDate": ""}], "Languages": [""], "Projects": [{"ProjectName": "Event Planner", "Description": "ActualProjectDThe concept of the project was to develop a product to manage and  handle events organized by the event manager. My role in the project was to develop the complete Back-End and Fort-end  functionalities like creating Api and model data structuree database with my technical skill set using frameworks of Javascript/n   like Angular, Node, MongoDB, and Express js.", "TechnologiesUsed": [""], "Role": ""}, {"ProjectName": "Sensor Data", "Description": "This was a complete node project to read the text file containing\n  the machine or sensor’s data & create a database of complete data\naccording to the client’s requirements. My role was to develop/n  functionalities for reading files and extracting data. So that it can be stored in the database and associated with seniors write clean/n  code and deliver a smooth product /n", "TechnologiesUsed": [""], "Role": ""}, {"ProjectName": "Sports Media", "Description": "The project was to integrate third-party sports API with a well- designed template that helps the user to update themselves with \n   the scoreboard other technologies used in the project were Angular for the designing of the front end & Node for the back end.\n In this project, I played the role of getting data from the API\n  integrating it into Back-End, and creating a new API with the help of\n  data coming from APIs.", "TechnologiesUsed": [""], "Role": ""}]}}