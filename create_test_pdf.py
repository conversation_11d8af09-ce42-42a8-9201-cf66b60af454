#!/usr/bin/env python3
"""
Create a simple test PDF for upload testing
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

def create_test_resume_pdf():
    filename = "test_resume_john_doe.pdf"
    
    # Create a simple PDF resume
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Title
    c.setFont("Helvetica-Bold", 24)
    c.drawString(50, height - 50, "John Doe")
    
    # Subtitle
    c.setFont("Helvetica", 16)
    c.drawString(50, height - 80, "Senior Software Engineer")
    
    # Contact Info
    c.setFont("Helvetica", 12)
    c.drawString(50, height - 120, "Email: <EMAIL>")
    c.drawString(50, height - 140, "Phone: +****************")
    c.drawString(50, height - 160, "Location: San Francisco, CA")
    
    # Professional Summary
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height - 200, "Professional Summary")
    c.set<PERSON>("Helvetica", 12)
    summary_text = [
        "Experienced software engineer with 5+ years of experience in full-stack development.",
        "Proficient in JavaScript, Python, React, and Node.js.",
        "Strong problem-solving skills and team collaboration experience."
    ]
    y_pos = height - 220
    for line in summary_text:
        c.drawString(50, y_pos, line)
        y_pos -= 20
    
    # Work Experience
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height - 300, "Work Experience")
    
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, height - 330, "Senior Software Engineer - Tech Corp (2020-2023)")
    c.setFont("Helvetica", 11)
    exp_text = [
        "• Developed and maintained web applications using React and Node.js",
        "• Led a team of 3 junior developers",
        "• Improved application performance by 40%"
    ]
    y_pos = height - 350
    for line in exp_text:
        c.drawString(60, y_pos, line)
        y_pos -= 15
    
    c.setFont("Helvetica-Bold", 12)
    c.drawString(50, height - 420, "Software Engineer - StartupXYZ (2018-2020)")
    c.setFont("Helvetica", 11)
    exp_text2 = [
        "• Built RESTful APIs using Python and Django",
        "• Implemented automated testing procedures",
        "• Collaborated with cross-functional teams"
    ]
    y_pos = height - 440
    for line in exp_text2:
        c.drawString(60, y_pos, line)
        y_pos -= 15
    
    # Education
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height - 510, "Education")
    c.setFont("Helvetica", 12)
    c.drawString(50, height - 530, "Bachelor of Science in Computer Science")
    c.drawString(50, height - 550, "University of California, Berkeley (2014-2018)")
    
    # Skills
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height - 590, "Skills")
    c.setFont("Helvetica", 12)
    skills_text = [
        "Programming Languages: JavaScript, Python, Java, C++",
        "Frameworks: React, Node.js, Django, Express",
        "Databases: MySQL, PostgreSQL, MongoDB",
        "Tools: Git, Docker, AWS, Jenkins"
    ]
    y_pos = height - 610
    for line in skills_text:
        c.drawString(50, y_pos, line)
        y_pos -= 20
    
    c.save()
    print(f"Created test resume PDF: {filename}")
    return filename

if __name__ == "__main__":
    create_test_resume_pdf()
