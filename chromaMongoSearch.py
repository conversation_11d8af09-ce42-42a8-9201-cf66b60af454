"""
ChromaDB + MongoDB Hybrid Search System

This module implements a two-stage search process:
1. MongoDB search excluding embedding factors to get MongoDB IDs
2. ChromaDB vector search on those specific MongoDB IDs using OpenAI embeddings
3. Return results with match distances

Usage:
    from chromaMongoSearch import ChromaMongoSearchEngine
    
    search_engine = ChromaMongoSearchEngine()
    results = await search_engine.search("Find software engineers with Python experience")
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from openai import OpenAI
from chromadb import HttpClient
from helperMongoDb import MongoDBClient
from bson import ObjectId
import asyncio

class ChromaMongoSearchEngine:
    """
    Hybrid search engine that combines MongoDB filtering with ChromaDB vector search.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2_chroma",
                 chroma_host: str = "localhost",
                 chroma_port: int = 8001,
                 chroma_collection: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the hybrid search engine.
        
        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            chroma_collection: ChromaDB collection name
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
        """
        self.database_name = database_name
        self.collection_name = collection_name
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize MongoDB client
        self.mongo_client = MongoDBClient(db_name=database_name)
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.chroma_collection = self.chroma_client.get_or_create_collection(chroma_collection)
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            # Use default OpenAI configuration
            self.openai_client = OpenAI()
        
        # Define embedding columns for vector search (15 total as requested)
        self.embedding_columns = [
            "FullNameEmbedding",
            "InstitutionEmbedding",
            "CompanyNameEmbedding",
            "RoleEmbedding",
            "Description/ResponsibilityEmbedding",
            "SkillsEmbedding",
            "CertificationNameEmbedding",
            "IssuingOrganizationEmbedding",
            "AchievementNameEmbedding",
            "ProjectNameEmbedding",
            "DescriptionEmbedding",
            "TechnologiesUsedEmbedding",
            "ProjectRoleEmbedding"
        ]

        # Map embedding columns to their corresponding embedding types in ChromaDB
        self.embedding_type_mapping = {
            "FullNameEmbedding": "FullName",
            "InstitutionEmbedding": "Institution",
            "CompanyNameEmbedding": "CompanyName",
            "RoleEmbedding": "Role",
            "Description/ResponsibilityEmbedding": "Description/Responsibility",
            "SkillsEmbedding": "Skills",
            "CertificationNameEmbedding": "CertificationName",
            "IssuingOrganizationEmbedding": "IssuingOrganization",
            "AchievementNameEmbedding": "AchievementName",
            "ProjectNameEmbedding": "ProjectName",
            "DescriptionEmbedding": "Description",
            "TechnologiesUsedEmbedding": "TechnologiesUsed",
            "ProjectRoleEmbedding": "ProjectRole"
        }
        
        # Define fields to exclude from MongoDB search (corresponding to embedding columns)
        self.excluded_mongodb_fields = [
            "Resume.PersonalInformation.FullName",
            "Resume.Education.Institution",
            "Resume.WorkExperience.CompanyName", 
            "Resume.WorkExperience.Role",
            "Resume.WorkExperience.Description/Responsibility",
            "Resume.Skills",
            "Resume.Certifications.CertificationName",
            "Resume.Certifications.IssuingOrganization",
            "Resume.Achievements.AchievementName",
            "Resume.Projects.ProjectName",
            "Resume.Projects.Description",
            "Resume.Projects.TechnologiesUsed",
            "Resume.Projects.Role"
        ]

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None



    def get_chromadb_docs_by_mongodb_ids(self, mongodb_ids: List[str]) -> Dict[str, Any]:
        """
        Get ChromaDB documents filtered by MongoDB IDs, focusing only on the 13 embedding fields.

        Args:
            mongodb_ids: List of MongoDB IDs to filter by

        Returns:
            ChromaDB query results filtered to only the 13 embedding types
        """
        try:
            if not mongodb_ids:
                return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

            # Define the 13 embedding types we want to search (without "Embedding" suffix)
            target_embedding_types = [
                "FullName",
                "Institution",
                "CompanyName",
                "Role",
                "Description/Responsibility",
                "Skills",
                "CertificationName",
                "IssuingOrganization",
                "AchievementName",
                "ProjectName",
                "Description",
                "TechnologiesUsed",
                "ProjectRole"
            ]

            # Query ChromaDB for documents with these MongoDB IDs AND specific embedding types
            results = self.chroma_collection.get(
                where={
                    "$and": [
                        {"mongodb_id": {"$in": mongodb_ids}},
                        {"embedding_type": {"$in": target_embedding_types}}
                    ]
                },
                include=["metadatas", "documents", "embeddings"]
            )

            self.logger.info(f"Found {len(results.get('ids', []))} ChromaDB documents for {len(mongodb_ids)} MongoDB IDs (13 embedding types only)")
            return results

        except Exception as e:
            self.logger.error(f"Error querying ChromaDB: {e}")
            return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

    async def generate_and_execute_mongodb_query(self, user_query: str, limit: int = 1000) -> List[str]:
        """
        Generate MongoDB query using OpenAI and execute it directly.

        Args:
            user_query: Natural language query from user
            limit: Maximum number of results to return

        Returns:
            List of MongoDB IDs as strings
        """
        try:
            print(f"\n🤖 GENERATING MONGODB QUERY WITH OPENAI...")
            print(f"   User Query: '{user_query}'")

            # OpenAI call to generate MongoDB query
            system_prompt = """
            You are a MongoDB query generator for resume search. Your task is to convert a natural language query
            into a MongoDB query that searches ONLY on non-embedding fields.

            Available non-embedding fields to search:
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages
            - Resume.Achievements.Date
            - Resume.Projects.Duration, TeamSize

            DO NOT search on these embedding fields (they will be handled separately):
            - FullName, Institution, CompanyName, Role, Description/Responsibility
            - Skills, CertificationName, IssuingOrganization, AchievementName
            - ProjectName, Description, TechnologiesUsed, ProjectRole

            Return a valid MongoDB query in JSON format. Use $regex for text matching and $and/$or for combinations.

            Examples:
            - "Find females" -> {"Resume.PersonalInformation.Gender": {"$regex": "^female$", "$options": "i"}}
            - "Teachers with degree" -> {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}
            - "5+ years experience" -> {"Resume.TotalWorkExperienceInYears": {"$regex": "^P[5-9]Y|^P[1-9][0-9]Y"}}
            - "People from Mumbai" -> {"Resume.PersonalInformation.City": {"$regex": "mumbai", "$options": "i"}}
            - "Female teachers from Delhi with 3+ years experience" -> {"$and": [{"Resume.PersonalInformation.Gender": {"$regex": "^female$", "$options": "i"}}, {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}, {"Resume.PersonalInformation.City": {"$regex": "delhi", "$options": "i"}}, {"Resume.TotalWorkExperienceInYears": {"$regex": "^P[3-9]Y|^P[1-9][0-9]Y"}}]}

            If no non-embedding fields are mentioned, return an empty object {}.
            Return ONLY valid JSON, no explanations.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Generate MongoDB query for: {user_query}"}
                ],
                temperature=0.1
            )

            query_text = response.choices[0].message.content.strip()
            print(f"   🔍 OpenAI Response: {query_text}")

            # Parse the JSON query
            try:
                mongodb_query = json.loads(query_text)
                print(f"   ✅ Parsed MongoDB Query: {mongodb_query}")
            except json.JSONDecodeError:
                print(f"   ❌ Failed to parse JSON, using empty query")
                mongodb_query = {}

            # Execute the MongoDB query
            print(f"\n🗄️  EXECUTING MONGODB QUERY...")
            collection = self.mongo_client.db[self.collection_name]

            # Use the generated MongoDB query, or fallback to all documents
            search_filter = mongodb_query if mongodb_query else {}
            print(f"   Query Filter: {search_filter}")

            # Get documents but only return the _id field
            cursor = collection.find(
                search_filter,
                {"_id": 1}
            ).limit(limit)

            mongodb_ids = [str(doc["_id"]) for doc in cursor]

            print(f"   📊 MongoDB Results: {len(mongodb_ids)} IDs found")
            if len(mongodb_ids) > 0:
                print(f"   📋 Sample IDs: {mongodb_ids[:3]}...")

            self.logger.info(f"Generated and executed MongoDB query: {search_filter}")
            self.logger.info(f"Found {len(mongodb_ids)} MongoDB IDs")

            return mongodb_ids

        except Exception as e:
            print(f"   ❌ Error in MongoDB query generation/execution: {e}")
            self.logger.error(f"Error in generate_and_execute_mongodb_query: {e}")

            # Fallback: return all document IDs
            try:
                print(f"   🔄 Falling back to all documents...")
                collection = self.mongo_client.db[self.collection_name]
                cursor = collection.find({}, {"_id": 1}).limit(limit)
                mongodb_ids = [str(doc["_id"]) for doc in cursor]
                print(f"   📊 Fallback Results: {len(mongodb_ids)} IDs found")
                return mongodb_ids
            except Exception as fallback_error:
                print(f"   ❌ Fallback also failed: {fallback_error}")
                self.logger.error(f"Fallback search also failed: {fallback_error}")
                return []


    async def parse_query_components(self, user_query: str) -> Dict[str, Any]:
        """
        Parse user query and classify each component into MongoDB or ChromaDB categories.

        Args:
            user_query: Natural language query from user

        Returns:
            Dictionary with parsed components for MongoDB and ChromaDB
        """
        try:
            system_prompt = """
            You are a query parser for resume search. Your task is to analyze a natural language query
            and classify each component into either MongoDB fields or ChromaDB embedding types.

            MongoDB Fields (non-embedding, structural data):
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages
            - Resume.Achievements.Date
            - Resume.Projects.Duration, TeamSize

            ChromaDB Embedding Types (content-based, semantic search):
            1. FullName - Person's name
            2. Institution - Educational institutions
            3. CompanyName - Company/organization names
            4. Role - Job titles and positions
            5. Description/Responsibility - Job responsibilities and descriptions
            6. Skills - Technical and soft skills
            7. CertificationName - Names of certifications
            8. IssuingOrganization - Organizations that issued certifications
            9. AchievementName - Names of achievements and awards
            10. ProjectName - Names of projects
            11. Description - Project descriptions
            12. TechnologiesUsed - Technologies and tools used in projects
            13. ProjectRole - Role in projects

            Return a JSON object with this structure:
            {
                "mongodb_components": {
                    "Gender": "male",
                    "City": "Mumbai"
                },
                "chromadb_components": {
                    "Role": ["PGT Physical", "Teacher"],
                    "Skills": ["MS Word", "MS Excel", "Python"],
                    "CompanyName": ["Google", "Microsoft"]
                }
            }

            Examples:
            - "Show male who worked as PGT Physical and have skill of MS Word, MS Excel"
              → mongodb: {"Gender": "male"}, chromadb: {"Role": ["PGT Physical"], "Skills": ["MS Word", "MS Excel"]}

            - "Find female software engineers from Mumbai with Python experience"
              → mongodb: {"Gender": "female", "City": "Mumbai"}, chromadb: {"Role": ["software engineer"], "Skills": ["Python"]}

            Return ONLY valid JSON, no explanations.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Parse this query: {user_query}"}
                ],
                temperature=0.7
            )

            parsed_text = response.choices[0].message.content.strip()
            print(f"🔍 DEBUG: Generated query components: {parsed_text}")

            # Parse JSON
            try:
                import json
                parsed_components = json.loads(parsed_text)

                self.logger.info(f"📋 Query parsed successfully:")
                self.logger.info(f"   MongoDB components: {parsed_components.get('mongodb_components', {})}")
                self.logger.info(f"   ChromaDB components: {parsed_components.get('chromadb_components', {})}")

                return parsed_components

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse query components: {parsed_text}")
                return {"mongodb_components": {}, "chromadb_components": {}}

        except Exception as e:
            self.logger.error(f"Error parsing query components: {e}")
            return {"mongodb_components": {}, "chromadb_components": {}}

    async def determine_embedding_types_to_search(self, user_query: str) -> List[str]:
        """
        Use OpenAI to determine which embedding types to search based on the query.

        Args:
            user_query: Original natural language query from user

        Returns:
            List of embedding types to search
        """
        try:
            system_prompt = """
            You are an embedding type selector for resume search. Your task is to analyze a natural language query
            and determine which specific embedding types should be searched for the best results.

            Available embedding types:
            1. FullName - Person's name (search when looking for specific people)
            2. Institution - Educational institutions (search when looking for graduates from specific schools)
            3. CompanyName - Company/organization names (search when looking for people from specific companies)
            4. Role - Job titles and positions (search when looking for specific job roles)
            5. Description/Responsibility - Job responsibilities (search when looking for specific work experience)
            6. Skills - Technical and soft skills (search when looking for specific skills/technologies)
            7. CertificationName - Names of certifications (search when looking for specific certifications)
            8. IssuingOrganization - Organizations that issued certifications (search when looking for certs from specific orgs)
            9. AchievementName - Names of achievements and awards (search when looking for specific achievements)
            10. ProjectName - Names of projects (search when looking for specific project names)
            11. Description - Project descriptions (search when looking for project experience)
            12. TechnologiesUsed - Technologies and tools used in projects (search when looking for specific tech experience)
            13. ProjectRole - Role in projects (search when looking for specific project roles)

            Examples:
            - "Find Dishita" → ["FullName"]
            - "Find Python developers" → ["Skills", "Role"]
            - "Find people from IIT" → ["Institution"]
            - "Find software engineers with React experience" → ["Role", "Skills", "TechnologiesUsed"]
            - "Find project managers from Google" → ["Role", "CompanyName"]
            - "Find AWS certified professionals" → ["CertificationName", "Skills"]

            Return ONLY a JSON array of embedding type names, nothing else.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Which embedding types should be searched for: {user_query}"}
                ],
                temperature=0.1
            )

            embedding_types_text = response.choices[0].message.content.strip()

            # Parse JSON array
            try:
                import json
                embedding_types = json.loads(embedding_types_text)

                # Validate embedding types
                valid_types = [
                    "FullName", "Institution", "CompanyName", "Role", "Description/Responsibility",
                    "Skills", "CertificationName", "IssuingOrganization", "AchievementName",
                    "ProjectName", "Description", "TechnologiesUsed", "ProjectRole"
                ]

                # Filter to only valid types
                filtered_types = [t for t in embedding_types if t in valid_types]

                if not filtered_types:
                    # Fallback to all types if none are valid
                    filtered_types = ["Skills", "Role", "FullName"]

                self.logger.info(f"Selected embedding types for '{user_query}': {filtered_types}")
                return filtered_types

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse embedding types: {embedding_types_text}")
                return ["Skills", "Role", "FullName"]  # Default fallback

        except Exception as e:
            self.logger.error(f"Error determining embedding types: {e}")
            return ["Skills", "Role", "FullName"]  # Default fallback


    async def verify_mongodb_filters(self, mongodb_ids: List[str], original_query: str) -> List[str]:
        """
        Verify that MongoDB IDs still match the original query filters.
        This is a post-processing step to ensure consistency.

        Args:
            mongodb_ids: List of MongoDB IDs to verify
            original_query: Original query to extract filters from

        Returns:
            Filtered list of MongoDB IDs that match the criteria
        """
        try:
            filters = self.extract_filters_from_query(original_query)
            if not filters:
                return mongodb_ids  # No filters to verify

            collection = self.mongo_client.db[self.collection_name]

            # Add _id filter to the existing filters
            from bson import ObjectId
            filters["_id"] = {"$in": [ObjectId(id_str) for id_str in mongodb_ids]}

            # Find documents that match both the ID list and the filters
            cursor = collection.find(filters, {"_id": 1})
            verified_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Verified {len(verified_ids)} out of {len(mongodb_ids)} MongoDB IDs against filters")
            return verified_ids

        except Exception as e:
            self.logger.error(f"Error verifying MongoDB filters: {e}")
            return mongodb_ids  # Return original list if verification fails
        
    async def determine_combination_logic(self, user_query: str, chromadb_components: Dict[str, List[str]]) -> str:
        """
        Decide whether to apply intersection, union, or mixed logic in ChromaDB search.
        """
        try:
            system_prompt = """
            You are an expert search logic planner. Based on the user's intent, you must decide:
            - 'intersection' if the user clearly wants candidates matching ALL semantic conditions.
            - 'union' if they are looking for candidates matching ANY condition (more relaxed).
            - 'mixed' if some conditions are required together and others can be optional.

            Examples:
            - "Python developers who worked at Google and used Django" → 'intersection'
            - "Teachers or software engineers with any skills in Java or React" → 'union'
            - "Female engineers from Mumbai who know either Python or JavaScript" → 'mixed'

            Return one of these: "intersection", "union", or "mixed".
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Query: {user_query}\nComponents: {json.dumps(chromadb_components)}"}
                ],
                temperature=0.3
            )
            logic_type = response.choices[0].message.content.strip().lower()
            if logic_type in {"intersection", "union", "mixed"}:
                return logic_type
            return "intersection"
        except Exception as e:
            self.logger.error(f"Error determining logic type: {e}")
            return "intersection"


    def perform_specific_component_searches(self,
                                        chromadb_components: Dict[str, List[str]],
                                        available_mongodb_ids: List[str],
                                        logic_type: str = "intersection",
                                        top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Perform vector-based semantic search using ChromaDB, supporting union, intersection, or mixed logic.

        Args:
            chromadb_components: Dict of embedding types and corresponding search terms.
            available_mongodb_ids: List of MongoDB IDs to filter against.
            logic_type: Logic type for combining search results ('union', 'intersection', or 'mixed').
            top_k: Number of top results to return.

        Returns:
            List of top-matched documents with metadata and similarity scores.
        """
        try:
            if not chromadb_components or not available_mongodb_ids:
                return []

            self.logger.info(f"🔍 Performing ChromaDB search with logic: {logic_type}")
            self.logger.info(f"   Components: {chromadb_components.keys()}")
            mongodb_id_to_distance = {}
            per_value_distance = {}

            if logic_type == "union":
                matching_mongodb_ids = set()
            else:
                matching_mongodb_ids = set(available_mongodb_ids)

            intersect_fields = {"Role", "CompanyName", "FullName"} if logic_type == "mixed" else set()

            for emb_type, search_values in chromadb_components.items():
                self.logger.info(f"🎯 Searching for {emb_type}: {search_values}")
                type_matching_ids = set()
                type_id_to_distance = {}

                for search_value in search_values:
                    search_embedding = self.get_embedding(search_value)
                    if not search_embedding:
                        continue

                    results = self.chroma_collection.query(
                        query_embeddings=[search_embedding],
                        n_results=100,
                        include=["metadatas", "distances"],
                        where={
                            "$and": [
                                {"mongodb_id": {"$in": list(available_mongodb_ids)}},
                                {"embedding_type": emb_type}
                            ]
                        }
                    )

                    if results.get("metadatas") and results["metadatas"][0]:
                        for metadata, distance in zip(results["metadatas"][0], results["distances"][0]):
                            if distance < 5:  # You may adjust the threshold
                                mongodb_id = metadata.get("mongodb_id")
                                if not mongodb_id:
                                    continue

                                type_matching_ids.add(mongodb_id)

                                if mongodb_id not in type_id_to_distance or distance < type_id_to_distance[mongodb_id]:
                                    type_id_to_distance[mongodb_id] = distance

                                key = (mongodb_id, emb_type, search_value)
                                if key not in per_value_distance or distance < per_value_distance[key]:
                                    per_value_distance[key] = distance

                # Merge with global result set based on logic type
                if logic_type == "intersection":
                    matching_mongodb_ids &= type_matching_ids
                elif logic_type == "union":
                    matching_mongodb_ids |= type_matching_ids
                elif logic_type == "mixed":
                    if emb_type in intersect_fields:
                        matching_mongodb_ids &= type_matching_ids
                    else:
                        matching_mongodb_ids |= type_matching_ids

                for mid in type_matching_ids:
                    best_dist = type_id_to_distance.get(mid)
                    if best_dist is not None:
                        if mid not in mongodb_id_to_distance or best_dist < mongodb_id_to_distance[mid]:
                            mongodb_id_to_distance[mid] = best_dist

                self.logger.info(f"   ✅ Matches for {emb_type}: {len(type_matching_ids)}, Remaining: {len(matching_mongodb_ids)}")

            if not matching_mongodb_ids:
                return []

            # Assemble final result list
            final_results = []
            for mongodb_id in matching_mongodb_ids:
                person_results = self.chroma_collection.get(
                    where={"mongodb_id": mongodb_id},
                    include=["metadatas", "documents"]
                )

                if person_results.get("metadatas"):
                    metadata = person_results["metadatas"][0]

                    chromadb_conditions = []
                    for emb_type, values in chromadb_components.items():
                        value_dist = []
                        for val in values:
                            dist = per_value_distance.get((mongodb_id, emb_type, val))
                            value_dist.append(f"{val} (distance: {dist:.3f})" if dist is not None else val)
                        chromadb_conditions.append(f"{emb_type}: [{', '.join(value_dist)}]")

                    final_results.append({
                        "mongodb_id": mongodb_id,
                        "full_name": metadata.get("FullName", "N/A"),
                        "email": metadata.get("Email", "N/A"),
                        "embedding_type": "Multiple Matches",
                        "match_distance": mongodb_id_to_distance.get(mongodb_id, None),
                        "similarity_score": mongodb_id_to_distance.get(mongodb_id, None),
                        "document_preview": "ChromaDB conditions:\n" + "\n".join(chromadb_conditions),
                        "original_filename": metadata.get("original_filename", "N/A")
                    })

            final_results.sort(key=lambda x: x["similarity_score"] or 9999)
            for i, res in enumerate(final_results[:top_k]):
                res["rank"] = i + 1

            self.logger.info(f"✅ Search complete. Returned {len(final_results[:top_k])} results.")
            return final_results[:top_k]

        except Exception as e:
            self.logger.error(f"Error in perform_specific_component_searches: {e}")
            return []


    async def search(self, nlp_query: str, top_k: int = 4, mongodb_limit: int = 1000) -> Dict[str, Any]:
        """
        Main search function that orchestrates the hybrid search process.
        
        Args:
            nlp_query: Natural language search query
            top_k: Number of top results to return
            mongodb_limit: Maximum MongoDB documents to consider
            
        Returns:
            Search results with metadata and match distances
        """
        try:
            self.logger.info(f"🚀 Starting smart hybrid search for query: '{nlp_query}'")

            # Stage 1: Parse query components using OpenAI
            self.logger.info("📋 Stage 1: Parsing query components with OpenAI...")
            parsed_components = await self.parse_query_components(nlp_query)

            mongodb_components = parsed_components.get("mongodb_components", {})
            chromadb_components = parsed_components.get("chromadb_components", {})

            # Print detailed parsing information
            print(f"\n🔍 QUERY ANALYSIS FOR: '{nlp_query}'")
            print("=" * 80)

            if mongodb_components:
                print("📊 MONGODB SEARCH (Structural Data):")
                for field, value in mongodb_components.items():
                    print(f"   {field}: {value}")
            else:
                print("📊 MONGODB SEARCH: No structural filters identified")

            if chromadb_components:
                print("\n🧠 CHROMADB SEARCH (Semantic/Content Data):")
                for emb_type, values in chromadb_components.items():
                    print(f"   {emb_type}: {values}")
            else:
                print("\n🧠 CHROMADB SEARCH: No semantic filters identified")

            # Stage 2: Generate and execute MongoDB query with OpenAI
            self.logger.info("🗄️  Stage 2: Generating and executing MongoDB query with OpenAI...")
            mongodb_ids = await self.generate_and_execute_mongodb_query(nlp_query, limit=mongodb_limit)

            print(f"   📊 MongoDB Results: {len(mongodb_ids)} IDs found")
            
            if not mongodb_ids:
                print("   ❌ No MongoDB documents found matching criteria")
                return {
                    "query": nlp_query,
                    "parsed_components": parsed_components,
                    "mongodb_query": "Generated by OpenAI",
                    "total_results": 0,
                    "results": [],
                    "error": "No MongoDB documents found"
                }

            # Stage 3: Perform ChromaDB component searches
            if chromadb_components:
                self.logger.info("🧠 Stage 3: Performing specific ChromaDB component searches...")

                print(f"\n🧠 CHROMADB COMPONENT SEARCH:")
                print(f"   Available MongoDB IDs: {len(mongodb_ids)}")
                print(f"   Searching for components: {list(chromadb_components.keys())}")

                # search_results = self.perform_specific_component_searches(chromadb_components, mongodb_ids, top_k)
                logic_type = await self.determine_combination_logic(nlp_query, chromadb_components)
                search_results = self.perform_specific_component_searches(chromadb_components, mongodb_ids, logic_type=logic_type, top_k=top_k)


                print(f"   🎯 Component search results: {len(search_results)} matches")

            else:
                # No ChromaDB components, just return MongoDB results
                self.logger.info("📊 Stage 3: No ChromaDB components, returning MongoDB results...")

                print(f"\n📊 RETURNING MONGODB-ONLY RESULTS:")
                print(f"   Found {len(mongodb_ids)} matching documents")

                # Get basic info for MongoDB-only results
                search_results = []
                for i, mongodb_id in enumerate(mongodb_ids[:top_k]):
                    # Try to get basic info from ChromaDB
                    person_results = self.chroma_collection.get(
                        where={"mongodb_id": mongodb_id},
                        include=["metadatas"],
                        limit=1
                    )

                    if person_results.get("metadatas"):
                        metadata = person_results["metadatas"][0]
                        search_results.append({
                            "rank": i + 1,
                            "mongodb_id": mongodb_id,
                            "full_name": metadata.get("FullName", "N/A"),
                            "email": metadata.get("Email", "N/A"),
                            "embedding_type": "MongoDB Match",
                            "match_distance": 0.0,
                            "similarity_score": 1.0,
                            "document_preview": f"Matches MongoDB criteria: {list(mongodb_components.keys())}",
                            "original_filename": metadata.get("original_filename", "N/A")
                        })

            # Print final results summary
            print(f"\n🎉 FINAL RESULTS SUMMARY:")
            print(f"   Total Results: {len(search_results)}")
            if search_results:
                print(f"   Top Match: {search_results[0]['full_name']}")
                print(f"   Match Type: {search_results[0]['embedding_type']}")

            # Return comprehensive results
            return {
                "query": nlp_query,
                "parsed_components": parsed_components,
                "mongodb_query": "Generated by OpenAI",
                "mongodb_components": mongodb_components,
                "chromadb_components": chromadb_components,
                "mongodb_ids_found": len(mongodb_ids),
                "total_results": len(search_results),
                "results": search_results,
                "search_stages": {
                    "stage_1": "Query components parsed with OpenAI",
                    "stage_2": f"MongoDB search: {len(mongodb_ids)} IDs found (Generated by OpenAI)",
                    "stage_3": f"ChromaDB component search: {len(search_results)} results"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {e}")
            return {
                "query": nlp_query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }

# Utility functions for easy usage
async def search_resumes(query: str, top_k: int = 4) -> Dict[str, Any]:
    """
    Convenience function to search resumes using the hybrid approach.
    
    Args:
        query: Natural language search query
        top_k: Number of top results to return
        
    Returns:
        Search results dictionary
    """
    search_engine = ChromaMongoSearchEngine()
    return await search_engine.search(query, top_k)

# Example usage
if __name__ == "__main__":
    async def main():
        # Example searches
        queries = [
            # "Find me a female who worked at shri g international and l g academy",
            # "Find me a femle teacher whose name is chandani and have experience of 1+ years",
            # "Find me a female whose name is chandani"
            "Female teachers from indore who know either hindi or physics"
        ]
        
        search_engine = ChromaMongoSearchEngine()
        
        for query in queries:
            print(f"\n{'='*60}")
            print(f"Searching: {query}")
            print('='*60)
            
            results = await search_engine.search(query, top_k=5)
            
            print(f"Original Query: {results.get('query')}")
            print(f"Optimized Query: {results.get('optimized_query')}")
            print(f"MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total Results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop Results:")
                for result in results['results'][:5]:  # Show top 3
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    print(f"     Preview: {result['document_preview'][:100]}...")
                    print()
            
            if results.get('error'):
                print(f"Error: {results['error']}")
    
    asyncio.run(main())
