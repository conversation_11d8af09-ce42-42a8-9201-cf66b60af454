"""
ChromaDB + MongoDB Hybrid Search System

This module implements a two-stage search process:
1. MongoDB search excluding embedding factors to get MongoDB IDs
2. ChromaDB vector search on those specific MongoDB IDs using OpenAI embeddings
3. Return results with match distances

Usage:
    from chromaMongoSearch import ChromaMongoSearchEngine
    
    search_engine = ChromaMongoSearchEngine()
    results = await search_engine.search("Find software engineers with Python experience")
"""

import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from openai import OpenAI
from chromadb import HttpClient
from helperMongoDb import MongoDBClient
from bson import ObjectId
import asyncio

class ChromaMongoSearchEngine:
    """
    Hybrid search engine that combines MongoDB filtering with ChromaDB vector search.
    """
    
    def __init__(self,
                 database_name: str = "dbProductionV2",
                 collection_name: str = "collectionResumeV2_chroma",
                 chroma_host: str = "localhost",
                 chroma_port: int = 8001,
                 chroma_collection: str = "resumes_by_type",
                 openai_api_key: Optional[str] = None,
                 openai_base_url: Optional[str] = None):
        """
        Initialize the hybrid search engine.
        
        Args:
            database_name: MongoDB database name
            collection_name: MongoDB collection name
            chroma_host: ChromaDB server host
            chroma_port: ChromaDB server port
            chroma_collection: ChromaDB collection name
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
        """
        self.database_name = database_name
        self.collection_name = collection_name
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize MongoDB client
        self.mongo_client = MongoDBClient(db_name=database_name)
        
        # Initialize ChromaDB client
        self.chroma_client = HttpClient(host=chroma_host, port=chroma_port)
        self.chroma_collection = self.chroma_client.get_or_create_collection(chroma_collection)
        
        # Initialize OpenAI client
        if openai_api_key and openai_base_url:
            self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
        else:
            # Use default OpenAI configuration
            self.openai_client = OpenAI()
        
        # Define embedding columns for vector search (15 total as requested)
        self.embedding_columns = [
            "FullNameEmbedding",
            "InstitutionEmbedding",
            "CompanyNameEmbedding",
            "RoleEmbedding",
            "Description/ResponsibilityEmbedding",
            "SkillsEmbedding",
            "CertificationNameEmbedding",
            "IssuingOrganizationEmbedding",
            "AchievementNameEmbedding",
            "ProjectNameEmbedding",
            "DescriptionEmbedding",
            "TechnologiesUsedEmbedding",
            "ProjectRoleEmbedding"
        ]

        # Map embedding columns to their corresponding embedding types in ChromaDB
        self.embedding_type_mapping = {
            "FullNameEmbedding": "FullName",
            "InstitutionEmbedding": "Institution",
            "CompanyNameEmbedding": "CompanyName",
            "RoleEmbedding": "Role",
            "Description/ResponsibilityEmbedding": "Description/Responsibility",
            "SkillsEmbedding": "Skills",
            "CertificationNameEmbedding": "CertificationName",
            "IssuingOrganizationEmbedding": "IssuingOrganization",
            "AchievementNameEmbedding": "AchievementName",
            "ProjectNameEmbedding": "ProjectName",
            "DescriptionEmbedding": "Description",
            "TechnologiesUsedEmbedding": "TechnologiesUsed",
            "ProjectRoleEmbedding": "ProjectRole"
        }
        
        # Define fields to exclude from MongoDB search (corresponding to embedding columns)
        self.excluded_mongodb_fields = [
            "Resume.PersonalInformation.FullName",
            "Resume.Education.Institution",
            "Resume.WorkExperience.CompanyName", 
            "Resume.WorkExperience.Role",
            "Resume.WorkExperience.Description/Responsibility",
            "Resume.Skills",
            "Resume.Certifications.CertificationName",
            "Resume.Certifications.IssuingOrganization",
            "Resume.Achievements.AchievementName",
            "Resume.Projects.ProjectName",
            "Resume.Projects.Description",
            "Resume.Projects.TechnologiesUsed",
            "Resume.Projects.Role"
        ]

    def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for the given text using OpenAI.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector or None if failed
        """
        if not text or not isinstance(text, str) or not text.strip():
            return None
        try:
            response = self.openai_client.embeddings.create(
                input=text.strip(),
                model="text-embedding-3-small"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Embedding error for '{text[:40]}...': {e}")
            return None

    async def search_mongodb_with_generated_query(self, mongodb_query: Dict[str, Any], limit: int = 1000) -> List[str]:
        """
        Search MongoDB using the generated query on NON-embedding fields only.

        Args:
            mongodb_query: Generated MongoDB query dictionary
            limit: Maximum number of results to return

        Returns:
            List of MongoDB IDs as strings
        """
        try:
            collection = self.mongo_client.db[self.collection_name]

            # Use the generated MongoDB query, or fallback to all documents
            search_filter = mongodb_query if mongodb_query else {}

            # Get documents but only return the _id field
            cursor = collection.find(
                search_filter,
                {"_id": 1}
            ).limit(limit)

            mongodb_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Found {len(mongodb_ids)} MongoDB IDs using generated query: {search_filter}")
            return mongodb_ids

        except Exception as e:
            self.logger.error(f"Error searching MongoDB with generated query: {e}")
            # Fallback: return all document IDs
            try:
                cursor = collection.find({}, {"_id": 1}).limit(limit)
                mongodb_ids = [str(doc["_id"]) for doc in cursor]
                self.logger.info(f"Fallback: returning {len(mongodb_ids)} MongoDB IDs")
                return mongodb_ids
            except Exception as fallback_error:
                self.logger.error(f"Fallback search also failed: {fallback_error}")
                return []

    def get_chromadb_docs_by_mongodb_ids(self, mongodb_ids: List[str]) -> Dict[str, Any]:
        """
        Get ChromaDB documents filtered by MongoDB IDs, focusing only on the 13 embedding fields.

        Args:
            mongodb_ids: List of MongoDB IDs to filter by

        Returns:
            ChromaDB query results filtered to only the 13 embedding types
        """
        try:
            if not mongodb_ids:
                return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

            # Define the 13 embedding types we want to search (without "Embedding" suffix)
            target_embedding_types = [
                "FullName",
                "Institution",
                "CompanyName",
                "Role",
                "Description/Responsibility",
                "Skills",
                "CertificationName",
                "IssuingOrganization",
                "AchievementName",
                "ProjectName",
                "Description",
                "TechnologiesUsed",
                "ProjectRole"
            ]

            # Query ChromaDB for documents with these MongoDB IDs AND specific embedding types
            results = self.chroma_collection.get(
                where={
                    "$and": [
                        {"mongodb_id": {"$in": mongodb_ids}},
                        {"embedding_type": {"$in": target_embedding_types}}
                    ]
                },
                include=["metadatas", "documents", "embeddings"]
            )

            self.logger.info(f"Found {len(results.get('ids', []))} ChromaDB documents for {len(mongodb_ids)} MongoDB IDs (13 embedding types only)")
            return results

        except Exception as e:
            self.logger.error(f"Error querying ChromaDB: {e}")
            return {"ids": [], "metadatas": [], "documents": [], "embeddings": []}

    def build_mongodb_query_from_components(self, mongodb_components: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build MongoDB query from parsed components.

        Args:
            mongodb_components: Dictionary of MongoDB field components

        Returns:
            MongoDB query dictionary
        """
        if not mongodb_components:
            return {}

        query_conditions = []

        for field, value in mongodb_components.items():
            if field == "Gender":
                # Use regex to match the whole word only (avoid matching 'female' when searching for 'male')
                gender_regex = f"^{value}$"
                query_conditions.append({
                    "Resume.PersonalInformation.Gender": {"$regex": gender_regex, "$options": "i"}
                })
            elif field == "City":
                query_conditions.append({
                    "Resume.PersonalInformation.City": {"$regex": value, "$options": "i"}
                })
            elif field == "State":
                query_conditions.append({
                    "Resume.PersonalInformation.State": {"$regex": value, "$options": "i"}
                })
            elif field == "Email":
                query_conditions.append({
                    "Resume.PersonalInformation.Email": {"$regex": value, "$options": "i"}
                })
            elif field == "Degree":
                query_conditions.append({
                    "Resume.Education.Degree": {"$regex": value, "$options": "i"}
                })
            elif field == "Languages":
                if isinstance(value, list):
                    lang_conditions = [{"Resume.Languages": {"$regex": lang, "$options": "i"}} for lang in value]
                    query_conditions.append({"$or": lang_conditions})
                else:
                    query_conditions.append({
                        "Resume.Languages": {"$regex": value, "$options": "i"}
                    })
            elif field == "ExperienceYears":
                # Handle experience years (e.g., "5+" -> "^P[5-9]Y|^P[1-9][0-9]Y")
                if "+" in str(value):
                    years = str(value).replace("+", "")
                    if years.isdigit():
                        pattern = f"^P[{years}-9]Y|^P[1-9][0-9]Y"
                        query_conditions.append({
                            "Resume.TotalWorkExperienceInYears": {"$regex": pattern}
                        })

        if len(query_conditions) == 1:
            return query_conditions[0]
        elif len(query_conditions) > 1:
            return {"$and": query_conditions}
        else:
            return {}

    # async def generate_mongodb_search_query(self, user_query: str) -> Dict[str, Any]:
    #     """
    #     First OpenAI call: Generate MongoDB search query focusing on NON-embedding fields.

    #     Args:
    #         user_query: Natural language query from user

    #     Returns:
    #         MongoDB query dictionary
    #     """
    #     try:
    #         system_prompt = """
    #         You are a MongoDB query generator for resume search. Your task is to convert a natural language query
    #         into a MongoDB query that searches ONLY on non-embedding fields.

    #         Available non-embedding fields to search:
    #         - Resume.PersonalInformation.Gender (Male/Female)
    #         - Resume.PersonalInformation.Email
    #         - Resume.PersonalInformation.Address, City, State
    #         - Resume.PersonalInformation.ContactNumber
    #         - Resume.PersonalInformation.BirthDate
    #         - Resume.PersonalInformation.Objective
    #         - Resume.Education.Degree, Specialization, GraduationYear, Percentage
    #         - Resume.WorkExperience.StartYear, EndYear, Duration, Location
    #         - Resume.TotalWorkExperienceInYears
    #         - Resume.Certifications.IssueDate, ExpiryDate
    #         - Resume.Languages
    #         - Resume.Achievements.Date
    #         - Resume.Projects.Duration, TeamSize

    #         DO NOT search on these embedding fields:
    #         - FullName, Institution, CompanyName, Role, Description/Responsibility
    #         - Skills, CertificationName, IssuingOrganization, AchievementName
    #         - ProjectName, Description, TechnologiesUsed, ProjectRole

    #         Return a valid MongoDB query in JSON format. Use $regex for text matching and $and/$or for combinations.

    #         Examples:
    #         - "Find females" -> {"Resume.PersonalInformation.Gender": {"$regex": "female", "$options": "i"}}
    #         - "Teachers with degree" -> {"Resume.Education.Degree": {"$regex": "education|teacher|bed", "$options": "i"}}
    #         - "5+ years experience" -> {"Resume.TotalWorkExperienceInYears": {"$regex": "^P[5-9]Y|^P[1-9][0-9]Y"}}
    #         """

    #         response = self.openai_client.chat.completions.create(
    #             model="gpt-4.1",
    #             messages=[
    #                 {"role": "system", "content": system_prompt},
    #                 {"role": "user", "content": f"Generate MongoDB query for: {user_query}"}
    #             ],
    #             temperature=0.1
    #         )

    #         query_text = response.choices[0].message.content.strip()

    #         # Try to parse as JSON
    #         try:
    #             import json
    #             mongodb_query = json.loads(query_text)
    #             self.logger.info(f"Generated MongoDB query: {mongodb_query}")
    #             return mongodb_query
    #         except json.JSONDecodeError:
    #             # Fallback to simple text search
    #             self.logger.warning(f"Could not parse MongoDB query, using fallback: {query_text}")
    #             return {}

    #     except Exception as e:
    #         self.logger.error(f"Error generating MongoDB query: {e}")
    #         return {}

    async def parse_query_components(self, user_query: str) -> Dict[str, Any]:
        """
        Parse user query and classify each component into MongoDB or ChromaDB categories.

        Args:
            user_query: Natural language query from user

        Returns:
            Dictionary with parsed components for MongoDB and ChromaDB
        """
        try:
            system_prompt = """
            You are a query parser for resume search. Your task is to analyze a natural language query
            and classify each component into either MongoDB fields or ChromaDB embedding types.

            MongoDB Fields (non-embedding, structural data):
            - Resume.PersonalInformation.Gender (Male/Female)
            - Resume.PersonalInformation.Email
            - Resume.PersonalInformation.Address, City, State
            - Resume.PersonalInformation.ContactNumber
            - Resume.PersonalInformation.BirthDate
            - Resume.PersonalInformation.Objective
            - Resume.Education.Degree, Specialization, GraduationYear, Percentage
            - Resume.WorkExperience.StartYear, EndYear, Duration, Location
            - Resume.TotalWorkExperienceInYears
            - Resume.Certifications.IssueDate, ExpiryDate
            - Resume.Languages
            - Resume.Achievements.Date
            - Resume.Projects.Duration, TeamSize

            ChromaDB Embedding Types (content-based, semantic search):
            1. FullName - Person's name
            2. Institution - Educational institutions
            3. CompanyName - Company/organization names
            4. Role - Job titles and positions
            5. Description/Responsibility - Job responsibilities and descriptions
            6. Skills - Technical and soft skills
            7. CertificationName - Names of certifications
            8. IssuingOrganization - Organizations that issued certifications
            9. AchievementName - Names of achievements and awards
            10. ProjectName - Names of projects
            11. Description - Project descriptions
            12. TechnologiesUsed - Technologies and tools used in projects
            13. ProjectRole - Role in projects

            Return a JSON object with this structure:
            {
                "mongodb_components": {
                    "Gender": "male",
                    "City": "Mumbai"
                },
                "chromadb_components": {
                    "Role": ["PGT Physical", "Teacher"],
                    "Skills": ["MS Word", "MS Excel", "Python"],
                    "CompanyName": ["Google", "Microsoft"]
                }
            }

            Examples:
            - "Show male who worked as PGT Physical and have skill of MS Word, MS Excel"
              → mongodb: {"Gender": "male"}, chromadb: {"Role": ["PGT Physical"], "Skills": ["MS Word", "MS Excel"]}

            - "Find female software engineers from Mumbai with Python experience"
              → mongodb: {"Gender": "female", "City": "Mumbai"}, chromadb: {"Role": ["software engineer"], "Skills": ["Python"]}

            Return ONLY valid JSON, no explanations.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Parse this query: {user_query}"}
                ],
                temperature=0.7
            )

            parsed_text = response.choices[0].message.content.strip()
            print(f"🔍 DEBUG: Generated query components: {parsed_text}")

            # Parse JSON
            try:
                import json
                parsed_components = json.loads(parsed_text)

                self.logger.info(f"📋 Query parsed successfully:")
                self.logger.info(f"   MongoDB components: {parsed_components.get('mongodb_components', {})}")
                self.logger.info(f"   ChromaDB components: {parsed_components.get('chromadb_components', {})}")

                return parsed_components

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse query components: {parsed_text}")
                return {"mongodb_components": {}, "chromadb_components": {}}

        except Exception as e:
            self.logger.error(f"Error parsing query components: {e}")
            return {"mongodb_components": {}, "chromadb_components": {}}

    async def determine_embedding_types_to_search(self, user_query: str) -> List[str]:
        """
        Use OpenAI to determine which embedding types to search based on the query.

        Args:
            user_query: Original natural language query from user

        Returns:
            List of embedding types to search
        """
        try:
            system_prompt = """
            You are an embedding type selector for resume search. Your task is to analyze a natural language query
            and determine which specific embedding types should be searched for the best results.

            Available embedding types:
            1. FullName - Person's name (search when looking for specific people)
            2. Institution - Educational institutions (search when looking for graduates from specific schools)
            3. CompanyName - Company/organization names (search when looking for people from specific companies)
            4. Role - Job titles and positions (search when looking for specific job roles)
            5. Description/Responsibility - Job responsibilities (search when looking for specific work experience)
            6. Skills - Technical and soft skills (search when looking for specific skills/technologies)
            7. CertificationName - Names of certifications (search when looking for specific certifications)
            8. IssuingOrganization - Organizations that issued certifications (search when looking for certs from specific orgs)
            9. AchievementName - Names of achievements and awards (search when looking for specific achievements)
            10. ProjectName - Names of projects (search when looking for specific project names)
            11. Description - Project descriptions (search when looking for project experience)
            12. TechnologiesUsed - Technologies and tools used in projects (search when looking for specific tech experience)
            13. ProjectRole - Role in projects (search when looking for specific project roles)

            Examples:
            - "Find Dishita" → ["FullName"]
            - "Find Python developers" → ["Skills", "Role"]
            - "Find people from IIT" → ["Institution"]
            - "Find software engineers with React experience" → ["Role", "Skills", "TechnologiesUsed"]
            - "Find project managers from Google" → ["Role", "CompanyName"]
            - "Find AWS certified professionals" → ["CertificationName", "Skills"]

            Return ONLY a JSON array of embedding type names, nothing else.
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-4.1",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Which embedding types should be searched for: {user_query}"}
                ],
                temperature=0.1
            )

            embedding_types_text = response.choices[0].message.content.strip()

            # Parse JSON array
            try:
                import json
                embedding_types = json.loads(embedding_types_text)

                # Validate embedding types
                valid_types = [
                    "FullName", "Institution", "CompanyName", "Role", "Description/Responsibility",
                    "Skills", "CertificationName", "IssuingOrganization", "AchievementName",
                    "ProjectName", "Description", "TechnologiesUsed", "ProjectRole"
                ]

                # Filter to only valid types
                filtered_types = [t for t in embedding_types if t in valid_types]

                if not filtered_types:
                    # Fallback to all types if none are valid
                    filtered_types = ["Skills", "Role", "FullName"]

                self.logger.info(f"Selected embedding types for '{user_query}': {filtered_types}")
                return filtered_types

            except json.JSONDecodeError:
                self.logger.warning(f"Could not parse embedding types: {embedding_types_text}")
                return ["Skills", "Role", "FullName"]  # Default fallback

        except Exception as e:
            self.logger.error(f"Error determining embedding types: {e}")
            return ["Skills", "Role", "FullName"]  # Default fallback

    # async def generate_chromadb_search_query(self, user_query: str) -> str:
    #     """
    #     Second OpenAI call: Generate ChromaDB search query focusing on the 13 embedding fields.

    #     Args:
    #         user_query: Original natural language query from user

    #     Returns:
    #         Optimized query for vector search on embedding fields
    #     """
    #     try:
    #         system_prompt = """
    #         You are a ChromaDB vector search query optimizer. Your task is to take a natural language query
    #         and convert it into a search query optimized for semantic vector search on these 13 resume embedding fields:

    #         1. FullName - Person's name
    #         2. Institution - Educational institutions
    #         3. CompanyName - Company/organization names
    #         4. Role - Job titles and positions
    #         5. Description/Responsibility - Job responsibilities and descriptions
    #         6. Skills - Technical and soft skills
    #         7. CertificationName - Names of certifications
    #         8. IssuingOrganization - Organizations that issued certifications
    #         9. AchievementName - Names of achievements and awards
    #         10. ProjectName - Names of projects
    #         11. Description - Project descriptions
    #         12. TechnologiesUsed - Technologies and tools used in projects
    #         13. ProjectRole - Role in projects

    #         Focus on extracting keywords related to:
    #         - Job titles, roles, positions
    #         - Skills, technologies, programming languages
    #         - Company names, institutions
    #         - Project types and descriptions
    #         - Certifications and achievements
    #         - Technical expertise areas

    #         Return a concise, keyword-rich query optimized for semantic similarity search.

    #         ## Note: Smartly understand what this query is asking for and then return the query. Like if query is asking for optimism than you have to smartly understand that optimism is a skill.
    #         """

    #         response = self.openai_client.chat.completions.create(
    #             model="gpt-4.1",
    #             messages=[
    #                 {"role": "system", "content": system_prompt},
    #                 {"role": "user", "content": f"Create vector search query for: {user_query}"}
    #             ],
    #             temperature=0.3
    #         )

    #         vector_query = response.choices[0].message.content.strip()
    #         print(f"🔍 DEBUG: Generated vector search query: {vector_query}")
    #         self.logger.info(f"Generated vector search query: '{user_query}' -> '{vector_query}'")
    #         return vector_query

    #     except Exception as e:
    #         self.logger.error(f"Error generating vector search query: {e}")
    #         return user_query  # Return original query if processing fails

    async def verify_mongodb_filters(self, mongodb_ids: List[str], original_query: str) -> List[str]:
        """
        Verify that MongoDB IDs still match the original query filters.
        This is a post-processing step to ensure consistency.

        Args:
            mongodb_ids: List of MongoDB IDs to verify
            original_query: Original query to extract filters from

        Returns:
            Filtered list of MongoDB IDs that match the criteria
        """
        try:
            filters = self.extract_filters_from_query(original_query)
            if not filters:
                return mongodb_ids  # No filters to verify

            collection = self.mongo_client.db[self.collection_name]

            # Add _id filter to the existing filters
            from bson import ObjectId
            filters["_id"] = {"$in": [ObjectId(id_str) for id_str in mongodb_ids]}

            # Find documents that match both the ID list and the filters
            cursor = collection.find(filters, {"_id": 1})
            verified_ids = [str(doc["_id"]) for doc in cursor]

            self.logger.info(f"Verified {len(verified_ids)} out of {len(mongodb_ids)} MongoDB IDs against filters")
            return verified_ids

        except Exception as e:
            self.logger.error(f"Error verifying MongoDB filters: {e}")
            return mongodb_ids  # Return original list if verification fails

    def perform_specific_component_searches(self, chromadb_components: Dict[str, List[str]],
                                          available_mongodb_ids: List[str],
                                          top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Perform specific component searches with AND logic for all conditions.

        Args:
            chromadb_components: Dictionary of ChromaDB components to search
            available_mongodb_ids: List of MongoDB IDs to filter by
            top_k: Number of top results to return

        Returns:
            List of search results with metadata and match distances
        """
        try:
            if not chromadb_components or not available_mongodb_ids:
                return []

            self.logger.info(f"🔍 Performing specific component searches:")
            for emb_type, values in chromadb_components.items():
                self.logger.info(f"   {emb_type}: {values}")

            matching_mongodb_ids = set(available_mongodb_ids)
            mongodb_id_to_distance = {}  # Track best (lowest) distance for each ID
            per_value_distance = {}  # (mongodb_id, embedding_type, search_value) -> distance

            for embedding_type, search_values in chromadb_components.items():
                self.logger.info(f"\n🎯 Searching {embedding_type} for: {search_values}")

                type_matching_ids = set()
                type_id_to_distance = {}

                for search_value in search_values:
                    search_embedding = self.get_embedding(search_value)

                    if search_embedding:
                        search_results = self.chroma_collection.query(
                            query_embeddings=[search_embedding],
                            n_results=100,  # Get many results to find all matches
                            include=["metadatas", "documents", "distances"],
                            where={
                                "$and": [
                                    {"mongodb_id": {"$in": list(matching_mongodb_ids)}},
                                    {"embedding_type": embedding_type}
                                ]
                            }
                        )

                        if search_results.get("metadatas") and search_results["metadatas"][0]:
                            for metadata, distance in zip(search_results["metadatas"][0], search_results["distances"][0]):
                                if distance < 5:  # Adjust threshold as needed
                                    mongodb_id = metadata.get("mongodb_id")
                                    if mongodb_id:
                                        type_matching_ids.add(mongodb_id)
                                        # Track the best (lowest) distance for this ID
                                        if mongodb_id not in type_id_to_distance or distance < type_id_to_distance[mongodb_id]:
                                            type_id_to_distance[mongodb_id] = distance
                                        # Track the distance for this value
                                        key = (mongodb_id, embedding_type, search_value)
                                        if key not in per_value_distance or distance < per_value_distance[key]:
                                            per_value_distance[key] = distance
                                        self.logger.info(f"   ✅ Found match: {metadata.get('source_text', '')} (distance: {distance:.3f})")

                # Apply AND logic: keep only IDs that match this embedding type
                matching_mongodb_ids = matching_mongodb_ids.intersection(type_matching_ids)
                # Update global best distances
                for mid in matching_mongodb_ids:
                    if mid in type_id_to_distance:
                        if mid not in mongodb_id_to_distance or type_id_to_distance[mid] < mongodb_id_to_distance[mid]:
                            mongodb_id_to_distance[mid] = type_id_to_distance[mid]
                self.logger.info(f"   📊 {len(type_matching_ids)} matches for {embedding_type}, {len(matching_mongodb_ids)} total remaining")

            self.logger.info(f"\n🎯 Final matching MongoDB IDs: {len(matching_mongodb_ids)}")

            if not matching_mongodb_ids:
                return []

            # Get the best results for the final matching IDs
            final_results = []
            for mongodb_id in matching_mongodb_ids:
                person_results = self.chroma_collection.get(
                    where={"mongodb_id": mongodb_id},
                    include=["metadatas", "documents"]
                )

                if person_results.get("metadatas"):
                    metadata = person_results["metadatas"][0]
                    document = person_results["documents"][0]

                    # Build ChromaDB conditions string with values and distances
                    chromadb_conditions = []
                    for emb_type, search_values in chromadb_components.items():
                        value_dist_pairs = []
                        for search_value in search_values:
                            dist = per_value_distance.get((mongodb_id, emb_type, search_value), None)
                            if dist is not None:
                                value_dist_pairs.append(f"{search_value} (distance: {dist:.3f})")
                            else:
                                value_dist_pairs.append(f"{search_value}")
                        chromadb_conditions.append(f"{emb_type}: [{', '.join(value_dist_pairs)}]")

                    final_results.append({
                        "mongodb_id": mongodb_id,
                        "full_name": metadata.get("FullName", "N/A"),
                        "email": metadata.get("Email", "N/A"),
                        "embedding_type": "Multiple Matches",
                        "match_distance": mongodb_id_to_distance.get(mongodb_id, None),
                        "similarity_score": mongodb_id_to_distance.get(mongodb_id, None),
                        "document_preview": (
                            "ChromaDB conditions:\n"
                            + "\n".join(chromadb_conditions)
                        ),
                        "original_filename": metadata.get("original_filename", "N/A")
                    })

            # Sort by name and add ranks
            final_results.sort(key=lambda x: x["full_name"])
            for i, result in enumerate(final_results[:top_k]):
                result["rank"] = i + 1

            self.logger.info(f"✅ Component search returned {len(final_results)} results matching ALL conditions")
            return final_results[:top_k]

        except Exception as e:
            self.logger.error(f"Error performing component search: {e}")
            return []

    async def search(self, nlp_query: str, top_k: int = 4, mongodb_limit: int = 1000) -> Dict[str, Any]:
        """
        Main search function that orchestrates the hybrid search process.
        
        Args:
            nlp_query: Natural language search query
            top_k: Number of top results to return
            mongodb_limit: Maximum MongoDB documents to consider
            
        Returns:
            Search results with metadata and match distances
        """
        try:
            self.logger.info(f"🚀 Starting smart hybrid search for query: '{nlp_query}'")

            # Stage 1: Parse query components using OpenAI
            self.logger.info("📋 Stage 1: Parsing query components with OpenAI...")
            parsed_components = await self.parse_query_components(nlp_query)

            mongodb_components = parsed_components.get("mongodb_components", {})
            chromadb_components = parsed_components.get("chromadb_components", {})

            # Print detailed parsing information
            print(f"\n🔍 QUERY ANALYSIS FOR: '{nlp_query}'")
            print("=" * 80)

            if mongodb_components:
                print("📊 MONGODB SEARCH (Structural Data):")
                for field, value in mongodb_components.items():
                    print(f"   {field}: {value}")
            else:
                print("📊 MONGODB SEARCH: No structural filters identified")

            if chromadb_components:
                print("\n🧠 CHROMADB SEARCH (Semantic/Content Data):")
                for emb_type, values in chromadb_components.items():
                    print(f"   {emb_type}: {values}")
            else:
                print("\n🧠 CHROMADB SEARCH: No semantic filters identified")

            # Stage 2: Build and execute MongoDB query
            self.logger.info("🗄️  Stage 2: Building and executing MongoDB query...")
            mongodb_query = self.build_mongodb_query_from_components(mongodb_components)

            print(f"\n🗄️  MONGODB QUERY GENERATED:")
            print(f"   {mongodb_query}")

            mongodb_ids = await self.search_mongodb_with_generated_query(mongodb_query, limit=mongodb_limit)

            print(f"   📊 MongoDB Results: {len(mongodb_ids)} IDs found")
            
            if not mongodb_ids:
                print("   ❌ No MongoDB documents found matching criteria")
                return {
                    "query": nlp_query,
                    "parsed_components": parsed_components,
                    "mongodb_query": mongodb_query,
                    "total_results": 0,
                    "results": [],
                    "error": "No MongoDB documents found"
                }

            # Stage 3: Perform ChromaDB component searches
            if chromadb_components:
                self.logger.info("🧠 Stage 3: Performing specific ChromaDB component searches...")

                print(f"\n🧠 CHROMADB COMPONENT SEARCH:")
                print(f"   Available MongoDB IDs: {len(mongodb_ids)}")
                print(f"   Searching for components: {list(chromadb_components.keys())}")

                search_results = self.perform_specific_component_searches(chromadb_components, mongodb_ids, top_k)

                print(f"   🎯 Component search results: {len(search_results)} matches")

            else:
                # No ChromaDB components, just return MongoDB results
                self.logger.info("📊 Stage 3: No ChromaDB components, returning MongoDB results...")

                print(f"\n📊 RETURNING MONGODB-ONLY RESULTS:")
                print(f"   Found {len(mongodb_ids)} matching documents")

                # Get basic info for MongoDB-only results
                search_results = []
                for i, mongodb_id in enumerate(mongodb_ids[:top_k]):
                    # Try to get basic info from ChromaDB
                    person_results = self.chroma_collection.get(
                        where={"mongodb_id": mongodb_id},
                        include=["metadatas"],
                        limit=1
                    )

                    if person_results.get("metadatas"):
                        metadata = person_results["metadatas"][0]
                        search_results.append({
                            "rank": i + 1,
                            "mongodb_id": mongodb_id,
                            "full_name": metadata.get("FullName", "N/A"),
                            "email": metadata.get("Email", "N/A"),
                            "embedding_type": "MongoDB Match",
                            "match_distance": 0.0,
                            "similarity_score": 1.0,
                            "document_preview": f"Matches MongoDB criteria: {list(mongodb_components.keys())}",
                            "original_filename": metadata.get("original_filename", "N/A")
                        })

            # Print final results summary
            print(f"\n🎉 FINAL RESULTS SUMMARY:")
            print(f"   Total Results: {len(search_results)}")
            if search_results:
                print(f"   Top Match: {search_results[0]['full_name']}")
                print(f"   Match Type: {search_results[0]['embedding_type']}")

            # Return comprehensive results
            return {
                "query": nlp_query,
                "parsed_components": parsed_components,
                "mongodb_query": mongodb_query,
                "mongodb_components": mongodb_components,
                "chromadb_components": chromadb_components,
                "mongodb_ids_found": len(mongodb_ids),
                "total_results": len(search_results),
                "results": search_results,
                "search_stages": {
                    "stage_1": "Query components parsed with OpenAI",
                    "stage_2": f"MongoDB search: {len(mongodb_ids)} IDs found",
                    "stage_3": f"ChromaDB component search: {len(search_results)} results"
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error in hybrid search: {e}")
            return {
                "query": nlp_query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }

# Utility functions for easy usage
async def search_resumes(query: str, top_k: int = 4) -> Dict[str, Any]:
    """
    Convenience function to search resumes using the hybrid approach.
    
    Args:
        query: Natural language search query
        top_k: Number of top results to return
        
    Returns:
        Search results dictionary
    """
    search_engine = ChromaMongoSearchEngine()
    return await search_engine.search(query, top_k)

# Example usage
if __name__ == "__main__":
    async def main():
        # Example searches
        queries = [
            # "Find me a female who worked at shri g international and l g academy",
            "Find me a femle teacher whose name is chandani and have experience of 1+ years",
            # "Find me a female whose name is chandani"
        ]
        
        search_engine = ChromaMongoSearchEngine()
        
        for query in queries:
            print(f"\n{'='*60}")
            print(f"Searching: {query}")
            print('='*60)
            
            results = await search_engine.search(query, top_k=5)
            
            print(f"Original Query: {results.get('query')}")
            print(f"Optimized Query: {results.get('optimized_query')}")
            print(f"MongoDB IDs Found: {results.get('mongodb_ids_found', 0)}")
            print(f"ChromaDB Docs Found: {results.get('chromadb_docs_found', 0)}")
            print(f"Total Results: {results.get('total_results', 0)}")
            
            if results.get('results'):
                print("\nTop Results:")
                for result in results['results'][:5]:  # Show top 3
                    print(f"  {result['rank']}. {result['full_name']} ({result['embedding_type']})")
                    print(f"     Similarity: {result['similarity_score']:.3f}")
                    print(f"     MongoDB ID: {result['mongodb_id']}")
                    print(f"     Preview: {result['document_preview'][:100]}...")
                    print()
            
            if results.get('error'):
                print(f"Error: {results['error']}")
    
    asyncio.run(main())
