import React, { useState, useEffect, useRef } from 'react';
import { FiBell, <PERSON>Sun, FiMoon, FiChevronDown, FiUser, FiHeart } from 'react-icons/fi';
import { useTheme } from '../contexts/ThemeContext';
import { useNotifications } from '../contexts/NotificationContext';
import './Header.css';

const Header = ({ user, onFavoritesClick }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [favoriteCount, setFavoriteCount] = useState(0);
  const userMenuRef = useRef(null);
  const notificationRef = useRef(null);
  const { isDarkMode, toggleTheme } = useTheme();

  // Use real notifications from context
  const {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications
  } = useNotifications();

  // Load favorite count from localStorage
  useEffect(() => {
    const updateFavoriteCount = () => {
      try {
        const favorites = localStorage.getItem('favoriteResumes');
        if (favorites) {
          const parsedFavorites = JSON.parse(favorites);
          setFavoriteCount(Object.keys(parsedFavorites).length);
        } else {
          setFavoriteCount(0);
        }
      } catch (error) {
        console.error('Error loading favorite count:', error);
        setFavoriteCount(0);
      }
    };

    updateFavoriteCount();

    // Listen for storage changes to update count in real-time
    const handleStorageChange = (e) => {
      if (e.key === 'favoriteResumes') {
        updateFavoriteCount();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom events from the same tab
    const handleFavoritesUpdate = () => {
      updateFavoriteCount();
    };

    window.addEventListener('favoritesUpdated', handleFavoritesUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('favoritesUpdated', handleFavoritesUpdate);
    };
  }, []);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false);
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle notification actions
  const handleNotificationClick = async (notification) => {
    if (notification.unread) {
      await markAsRead(notification.id);
    }
  };

  const handleMarkAllRead = async () => {
    await markAllAsRead();
  };

  const handleClearAll = async () => {
    await clearAllNotifications();
    setShowNotifications(false);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'upload': return '📄';
      case 'duplicate': return '⚠️';
      case 'error': return '❌';
      case 'search': return '🔍';
      case 'no_results': return '🔍';
      default: return '📢';
    }
  };

  return (
    <header className="header">
      <div className="header-content">
        {/* Header Title or Logo Space */}
        <div className="header-left">
          {/* This space can be used for a logo or title if needed */}
        </div>

        {/* Right Side Actions */}
        <div className="header-actions">
          {/* Favorites */}
          <button
            className="favorites-btn"
            onClick={onFavoritesClick}
            title="View Favorite Resumes"
          >
            <FiHeart size={20} />
            {favoriteCount > 0 && (
              <span className="favorites-badge">{favoriteCount}</span>
            )}
          </button>

          {/* Notifications */}
          <div className="notification-container" ref={notificationRef}>
            <button
              className="notification-btn"
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <FiBell size={20} />
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount}</span>
              )}
            </button>

            {showNotifications && (
              <div className="notification-dropdown">
                <div className="notification-header">
                  <h3>Notifications</h3>
                  <div className="notification-actions">
                    <span className="notification-count">{unreadCount} new</span>
                    {unreadCount > 0 && (
                      <button
                        className="mark-all-read-btn"
                        onClick={handleMarkAllRead}
                        title="Mark all as read"
                      >
                        Mark all read
                      </button>
                    )}
                  </div>
                </div>
                <div className="notification-list">
                  {loading ? (
                    <div className="notification-loading">Loading notifications...</div>
                  ) : notifications.length === 0 ? (
                    <div className="notification-empty">No notifications yet</div>
                  ) : (
                    notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`notification-item ${notification.unread ? 'unread' : ''}`}
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="notification-icon">
                          {getNotificationIcon(notification.type)}
                        </div>
                        <div className="notification-content">
                          <h4 className="notification-title">{notification.title}</h4>
                          <p className="notification-message">{notification.message}</p>
                          <span className="notification-time">{notification.time_ago}</span>
                        </div>
                        {notification.unread && <div className="unread-dot" />}
                        <button
                          className="notification-delete"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteNotification(notification.id);
                          }}
                          title="Delete notification"
                        >
                          ×
                        </button>
                      </div>
                    ))
                  )}
                </div>
                <div className="notification-footer">
                  {notifications.length > 0 && (
                    <button
                      className="clear-all-btn"
                      onClick={handleClearAll}
                    >
                      Clear all notifications
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Theme Toggle */}
          <button
            className="theme-toggle-btn"
            onClick={toggleTheme}
            title={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
          >
            {isDarkMode ? <FiSun size={20} /> : <FiMoon size={20} />}
          </button>

          {/* User Menu */}
          <div className="user-menu-container" ref={userMenuRef}>
            <button
              className="user-menu-btn"
              onClick={() => setShowUserMenu(!showUserMenu)}
            >
              <div className="user-avatar">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.name} />
                ) : (
                  <FiUser size={18} />
                )}
              </div>
              <div className="user-info">
                <span className="user-name">{user.name}</span>
                <span className="user-role">Administrator</span>
              </div>
              <FiChevronDown size={16} className="dropdown-icon" />
            </button>

            {showUserMenu && (
              <div className="user-dropdown">
                <div className="user-dropdown-header">
                  <div className="user-avatar large">
                    {user.avatar ? (
                      <img src={user.avatar} alt={user.name} />
                    ) : (
                      <FiUser size={24} />
                    )}
                  </div>
                  <div className="user-details">
                    <h4>{user.name}</h4>
                    <p>{user.email}</p>
                  </div>
                </div>
                <div className="user-dropdown-menu">
                  <a href="/profile" className="dropdown-item">
                    <FiUser size={16} />
                    Profile Settings
                  </a>
                  <hr className="dropdown-divider" />
                  <button className="dropdown-item logout">
                    Sign Out
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
